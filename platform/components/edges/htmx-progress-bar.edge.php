@props([
    'progress_id' => 'progress_' . rand(0,10000),
    'status_text' => 'Processing your request...',
    'progress_endpoint' => '',
    'complete_endpoint' => '',
    'initial_progress' => 0,
    'status' => 'running'
])

@php
// Determine the current state based on status
$is_running = $status === 'running';
$is_complete = $status === 'complete';
$is_error = $status === 'error';
$is_cancelled = $status === 'cancelled';
$progress_value = $initial_progress ?? 0;
@endphp

@if($is_complete)
    {{-- Complete State --}}
    <div hx-trigger="done" hx-get="{{ $complete_endpoint }}" hx-swap="outerHTML" hx-target="this">
        <h3 role="status" id="{{ $progress_id }}_label" tabindex="-1" autofocus class="text-lg font-medium text-green-600 mb-4">
            ✅ Complete
        </h3>

        <div class="mb-4">
            <div class="progress bg-gray-200 rounded-full h-2" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="100" aria-labelledby="{{ $progress_id }}_label">
                <div id="{{ $progress_id }}_bar" class="progress-bar bg-green-500 h-full rounded-full transition-all duration-300 ease-out" style="width:100%"></div>
            </div>
        </div>

        <div class="text-sm text-green-700 mb-4">
            {{ $status_text }}
        </div>

        <button id="restart-btn" class="btn btn-primary bg-indigo-600 hover:bg-indigo-500 text-white px-4 py-2 rounded-md text-sm font-semibold shadow-sm transition-colors duration-200"
                hx-post="{{ str_replace('/save_nav_entry_complete', '/add_nav_entry', $complete_endpoint) }}"
                hx-target="this"
                hx-swap="outerHTML"
                classes="add show:600ms">
            Add Another Entry
        </button>
    </div>
@elseif($is_error)
    {{-- Error State --}}
    <div>
        <h3 role="status" id="{{ $progress_id }}_label" tabindex="-1" autofocus class="text-lg font-medium text-red-600 mb-4">
            ❌ Error
        </h3>

        <div class="mb-4">
            <div class="progress bg-gray-200 rounded-full h-2" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0" aria-labelledby="{{ $progress_id }}_label">
                <div id="{{ $progress_id }}_bar" class="progress-bar bg-red-500 h-full rounded-full transition-all duration-300 ease-out" style="width:0%"></div>
            </div>
        </div>

        <div class="text-sm text-red-700 mb-4">
            {{ $status_text }}
        </div>

        <button id="restart-btn" class="btn btn-primary bg-indigo-600 hover:bg-indigo-500 text-white px-4 py-2 rounded-md text-sm font-semibold shadow-sm transition-colors duration-200"
                hx-post="{{ str_replace('/save_nav_entry_complete', '/add_nav_entry', $complete_endpoint) }}"
                hx-target="this"
                hx-swap="outerHTML"
                classes="add show:600ms">
            Try Again
        </button>
    </div>
@elseif($is_cancelled)
    {{-- Cancelled State --}}
    <div>
        <h3 role="status" id="{{ $progress_id }}_label" tabindex="-1" autofocus class="text-lg font-medium text-gray-600 mb-4">
            ⏹️ Cancelled
        </h3>

        <div class="mb-4">
            <div class="progress bg-gray-200 rounded-full h-2" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0" aria-labelledby="{{ $progress_id }}_label">
                <div id="{{ $progress_id }}_bar" class="progress-bar bg-gray-500 h-full rounded-full transition-all duration-300 ease-out" style="width:0%"></div>
            </div>
        </div>

        <div class="text-sm text-gray-700 mb-4">
            {{ $status_text }}
        </div>

        <button id="restart-btn" class="btn btn-primary bg-indigo-600 hover:bg-indigo-500 text-white px-4 py-2 rounded-md text-sm font-semibold shadow-sm transition-colors duration-200"
                hx-post="{{ str_replace('/save_nav_entry_complete', '/add_nav_entry', $complete_endpoint) }}"
                hx-target="this"
                hx-swap="outerHTML"
                classes="add show:600ms">
            Start Over
        </button>
    </div>
@else
    {{-- Running State --}}
    <div   hx-get="{{ $progress_endpoint }}"
           hx-trigger="every 600ms"
           hx-target="this"
           hx-swap="outerHTML">
        <h3 role="status" id="{{ $progress_id }}_label" tabindex="-1" autofocus class="text-lg font-medium text-gray-900 mb-4">
            ⏳ {{ $status_text }}
        </h3>

        <div hx-trigger="done" hx-get="{{ $complete_endpoint }}" hx-swap="outerHTML" hx-target="this" class="mb-4">
            <div class="progress bg-gray-200 rounded-full h-2" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="{{ $progress_value }}" aria-labelledby="{{ $progress_id }}_label">
                <div id="{{ $progress_id }}_bar" class="progress-bar bg-indigo-600 h-full rounded-full transition-all duration-300 ease-out" style="width:{{ $progress_value }}%"></div>
            </div>
        </div>

        <div class="text-sm text-gray-600">
            <span class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Please wait while we process your request...
            </span>
        </div>
    </div>
@endif
