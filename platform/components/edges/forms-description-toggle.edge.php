
@props([
    'title' => 'form description toggle',
    'description' => 'Toggle button for showing field descriptions',
    'field_name' => '',
    'description_text' => '',
    'class_suffix' => '',
])

<div x-data="{ activeDescription: false }">
    <div class="flex items-center">
        <button type="button"
                @click="activeDescription = !activeDescription"
                class="ml-2 text-gray-400 hover:text-gray-500 {{ $class_suffix }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>
    <div x-show="activeDescription"
         x-transition
         @click.away="activeDescription = false"
         class="absolute z-10 mt-1 bg-white rounded-md shadow-lg p-3 text-sm text-gray-600 border border-gray-200 max-w-xs">
        {{ $description_text }}
    </div>
</div>
