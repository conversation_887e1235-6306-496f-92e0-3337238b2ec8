@props([
    'name' => 'template',
    'label' => 'Email Template',
    'selected' => '',
    'campaign_id' => null,
    'show_actions' => true,
    'show_data_source' => true,
    'data_source_name' => 'data_source_id',
    'selected_data_source' => '',
    'onchange' => null
])

@php
// Get templates from the template folder and subdirectories
$template_folder = FS_APP_ROOT . '/resources/email_templates/';
$templates = [];

if (is_dir($template_folder)) {
    // Get files directly in the template folder
    $files = glob($template_folder . '*.{html,htm,emlt.php}', GLOB_BRACE);
    foreach ($files as $file) {
        $filename = basename($file);
        $template_name = pathinfo($filename, PATHINFO_FILENAME);
        // Remove .emlt from .emlt.php files
        $template_name = str_replace('.emlt', '', $template_name);
        $templates[$filename] = ucwords(str_replace(['_', '-'], ' ', $template_name));
    }

    // Also get files from subdirectories
    $subdirs = glob($template_folder . '*', GLOB_ONLYDIR);
    foreach ($subdirs as $subdir) {
        $subdir_name = basename($subdir);
        $subfiles = glob($subdir . '/*.{html,htm,emlt.php}', GLOB_BRACE);
        foreach ($subfiles as $file) {
            $filename = $subdir_name . '/' . basename($file);
            $template_name = pathinfo(basename($file), PATHINFO_FILENAME);
            // Remove .emlt from .emlt.php files
            $template_name = str_replace('.emlt', '', $template_name);
            $display_name = ucwords(str_replace(['_', '-'], ' ', $subdir_name)) . ' - ' . ucwords(str_replace(['_', '-'], ' ', $template_name));
            $templates[$filename] = $display_name;
        }
    }
}

// Also get campaign-specific templates if campaign_id is provided
if ($campaign_id) {
    // This would get templates from the database for this specific campaign
    // For now, we'll just use file-based templates
}
@endphp

<div class="space-y-2">
    <div class="flex space-x-2">
        <div class="flex-1">
            <x-forms-select
                name="{{ $name }}"
                :id="$name"
                label="{{ $label }}"
                :options="array_merge(['' => 'Select a template...'], $templates)"
                :selected="$selected"
                hx-trigger="change"
                :hx-get="APP_ROOT . '/api/email_campaigns/template_preview'"
                hx-target="#template_preview"
                hx-include="this"
            />
            <!-- Template Selection -->
           {{-- <select name="{{ $name }}"
                    id="{{ $name }}"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    @if($onchange) onchange="{{ $onchange }}" @endif>
                <option value="">Select a template...</option>
                @foreach($templates as $filename => $display_name)
                    <option value="{{ $filename }}" {{ $selected === $filename ? 'selected' : '' }}>
                        {{ $display_name }}
                    </option>
                @endforeach
            </select>
            --}}
        </div>

        <!-- Data Source Selection -->
        @if($show_data_source)
            <div class="flex-1">
                <x-data-source-selector
                    :name="$data_source_name"
                    label="Data Source"
                    :selected="$selected_data_source"
                    :show_preview="false"
                    :show_create_new="true"
                    :campaign_id="$campaign_id"
                />
            </div>
        @endif
    </div>

    <!-- Enhanced Actions with Data Source Integration -->
    @if($show_actions)
        <div class="mt-3 flex items-center space-x-3">
            <button type="button"
                    class="text-sm text-indigo-600 hover:text-indigo-500"
                    onclick="previewTemplate()">
                Preview Template
            </button>
            @if($show_data_source)
                <span class="text-gray-300">|</span>
                <button type="button"
                        class="text-sm text-indigo-600 hover:text-indigo-500"
                        onclick="testWithDataSource()">
                    Test with Data Source
                </button>
            @endif
        </div>

        <div class="flex space-x-1">
            <!-- Edit Template Button -->
            <button type="button"
                    hx-get="{{ APP_ROOT }}/api/email_campaigns/edit_template_check"
                    hx-include="[name='{{ $name }}']"
                    hx-target="#modal_body"
                    hx-vals='{"campaign_id": "{{ $campaign_id }}"}'
                    @click="showModal = true"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    title="Edit Selected Template"
                    onclick="console.log('Template selector value:', document.querySelector('[name=\'{{ $name }}\']')?.value);">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </button>

            <!-- New Template Button -->
            <button type="button"
                    hx-get="{{ APP_ROOT }}/api/email_campaigns/template_editor"
                    hx-vals='{"mode": "new", "template": "", "campaign_id": "{{ $campaign_id }}"}'
                    hx-target="#modal_body"
                    @click="showModal = true"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    title="Create New Template"
            >
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>

            <!-- Preview Template Button -->
            <button type="button"
                    hx-get="{{ APP_ROOT }}/api/email_campaigns/template_preview"
                    hx-include="[name='{{ $name }}']"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    title="Preview Template in New Window">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
            </button>

            @if($show_data_source)
            <!-- Test with Data Source Button -->
            <button type="button"
                    hx-get="{{ APP_ROOT }}/api/email_campaigns/template_test_window"
                    hx-include="[name='{{ $name }}'], [name='{{ $data_source_name }}']"
                    hx-target="this"
                    hx-swap="none"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    title="Test Template with Data Source">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </button>
            @endif
        </div>
    @endif
    
    @if($show_actions)
    <!-- Template Preview - Initially hidden, HTMX will replace entire element -->
    <div id="template_preview" class="hidden mt-2 p-3 bg-gray-50 border rounded-md">
        <div class="text-sm text-gray-600">
            <strong>Preview:</strong>
            <div id="template_preview_content" class="mt-1 max-h-32 overflow-y-auto text-xs"></div>
        </div>
    </div>
    @endif
</div>

