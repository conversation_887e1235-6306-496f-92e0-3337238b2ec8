@props([
    'title' => 'Data Source Builder',
    'description' => 'Configure database data sources for tables and email campaigns',
    'data_source_id' => null,
    'data_source' => null, // Allow pre-populated data source
    'mode' => 'create', // 'create' or 'edit'
    'redirect_url' => null
])

@php


    // Use provided data_source or load from database for edit mode
    if (!$data_source && $mode === 'edit' && $data_source_id) {
        $data_source = data_source_manager::get_data_source($data_source_id);
    }

    $available_tables = data_source_manager::get_available_tables();

    // Category options
    $category_options = [
        'data_table' => 'Data Tables',
        'email' => 'Email & Campaigns',
        'users' => 'User Management',
        'system' => 'System Tables',
        'autodesk' => 'Autodesk Integration',
        'other' => 'Other'
    ];

    // Filter operators
    $filter_operators = [
        '=' => 'Equals',
        '!=' => 'Not Equals',
        '>' => 'Greater Than',
        '>=' => 'Greater Than or Equal',
        '<' => 'Less Than',
        '<=' => 'Less Than or Equal',
        'LIKE' => 'Contains',
        'NOT LIKE' => 'Does Not Contain',
        'IN' => 'In List',
        'NOT IN' => 'Not In List',
        'IS NULL' => 'Is Empty',
        'IS NOT NULL' => 'Is Not Empty'
    ];
@endphp
<div class="mb-8">
    <div class="flex items-center justify-between">
        <div>
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-4">
                    <li>
                        <button hx-get="{{ APP_ROOT }}/data_sources"
                                hx-target="#content_wrapper"
                                class="text-gray-400 hover:text-gray-500">
                            Data Sources
                        </button>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-4 text-sm font-medium text-gray-500">Edit Data Source</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<form hx-post="{{ APP_ROOT }}/api/data_sources/{{ $mode === 'edit' ? 'update' : 'create_data_source' }}"
      hx-target="#test-result"
      hx-swap="outerHTML"
      class="space-y-6">
    <div class="p-6 mx-auto bg-gray-200">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900">{{ $title }}</h1>
            <p class="mt-1 text-sm text-gray-600">{{ $description }}</p>
        </div>

        <!-- Form -->

        @if($mode === 'edit' && $data_source_id)
            <input type="hidden" name="id" value="{{ $data_source_id }}">
        @endif
        <div class="grid grid-cols-1 gap-6 2xl:grid-cols-2">

            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                     onclick="toggleSection('basic-info')">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900">Basic Information</h2>
                        <svg id="basic-info-icon"
                             class="h-5 w-5 text-gray-400 transform transition-transform duration-200" fill="none"
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
                <div id="basic-info-content" class="collapsible-content p-6">

                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <x-forms-input
                                    name="name"
                                    label="Name *"
                                    :value="$data_source['name'] ?? ''"
                                    placeholder="Enter data source name"
                                    required
                            />
                        </div>

                        <div>
                            <x-forms-select
                                    name="category"
                                    label="Category"
                                    :options="$category_options"
                                    :selected="$data_source['category'] ?? 'other'"
                            />
                        </div>

                        <div class="sm:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Data Source Type *</label>
                            @php
                                $data_source_type = $data_source['data_source_type'] ?? 'standard';
                                $data_source_type_options = [
                                    'standard' => 'Standard Multi-Table (with joins)',
                                    'multi_table_merger' => 'Multi-Table Merger (unified dataset)'
                                ];
                            @endphp
                            <x-forms-select
                                name="data_source_type"
                                :options="$data_source_type_options"
                                :selected="$data_source_type"
                                hx-post="{{ APP_ROOT }}/api/data_sources/update_data_source_type"
                                hx-target="#table-configuration-container"
                                hx-swap="innerHTML"
                                hx-include="form"
                                class_suffix="mb-4"
                            />
                        </div>

                        <div id="table-configuration-container" class="sm:col-span-2">
                            @if($data_source_type === 'multi_table_merger')
                                <x-multi-table-merger-configuration
                                    :available_tables="$available_tables"
                                    :data_source="$data_source"
                                />
                            @else
                                <label class="block text-sm font-medium text-gray-700 mb-3">Database Tables *</label>
                                @php
                                    $selected_tables = [];
                                    if ($data_source && !empty($data_source['tables'])) {
                                        $selected_tables = $data_source['tables'];
                                    } elseif ($data_source && !empty($data_source['table_name'])) {
                                        $selected_tables = [$data_source['table_name']];
                                    }

                                    $table_aliases = [];
                                    if ($data_source && !empty($data_source['table_aliases'])) {
                                        $table_aliases = $data_source['table_aliases'];
                                    }
                                @endphp
                                <x-data-source-table-selection
                                        :available_tables="$available_tables"
                                        :selected_tables="$selected_tables"
                                        :table_aliases="$table_aliases"
                                />
                            @endif
                        </div>

                        <div class="sm:col-span-1 lg:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea name="description"
                                      rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                      placeholder="Optional description of this data source">{{ $data_source['description'] ?? '' }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div id="preview-slot" class="gap-6">
                <!-- Query Preview -->
                <div class="bg-white shadow rounded-t-lg">
                    <div class="px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">SQL Query Preview</h2>
                            <div class="flex items-center space-x-2">
                                <button type="button"
                                        hx-post="{{ APP_ROOT }}/api/data_sources/update_query_preview"
                                        hx-target="#query-preview-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Update Query
                                </button>
                            </div>
                        </div>
                    </div>
                    <div id="query-preview-content" class="p-6 ">

                        <div id="query-preview-container"
                             @if($data_source && ($data_source['tables'] ?? $data_source['table_name'] ?? null))
                             hx-get="{{ APP_ROOT }}/api/data_sources/query_preview_fragment"
                             hx-trigger="load"
                             hx-target="#query-preview-container"
                             hx-swap="innerHTML"
                             hx-include="form"
                             @endif>
                            @if($data_source && ($data_source['tables'] ?? $data_source['table_name'] ?? null))
                                <div class="text-center py-8 text-gray-500">
                                    <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <div class="mt-2">Loading SQL query...</div>
                                </div>
                            @else
                                <div class="text-center py-8 text-gray-500">
                                    Select a table and configure filters to see the SQL query
                                </div>
                            @endif
                        </div>
                    </div>
                </div>


                <!-- Data Preview -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Data Preview</h2>
                            <div class="flex items-center space-x-2">
                                <button type="button"
                                        hx-get="{{ APP_ROOT }}/api/data_sources/data_preview_fragment"
                                        hx-target="#preview-container"
                                        hx-swap="innerHTML"
                                        hx-include="form"
                                        hx-indicator="#preview-loading"
                                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Refresh Preview
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="preview-container"
                         @if($data_source && !empty($selected_tables))
                         hx-get="{{ APP_ROOT }}/api/data_sources/data_preview_fragment"
                         hx-trigger="load"
                         hx-target="#preview-container"
                         hx-swap="innerHTML"
                         hx-include="form"
                         @endif>
                        @if($data_source && !empty($selected_tables))
                            <div class="text-center py-8 text-gray-500">
                                <div class="animate-spin inline-block w-6 h-6 border-[3px] border-current border-t-transparent text-blue-600 rounded-full" role="status" aria-label="loading">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                <div class="mt-2">Loading data preview...</div>
                            </div>
                        @else
                            <div class="text-center py-8 text-gray-500">
                                Select tables and configure joins to preview data
                            </div>
                        @endif
                    </div>
                </div>


            </div>

        </div>
        <div class="bg-white shadow rounded-b-lg">
            <!-- Table Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                     onclick="toggleSection('table-info')">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900">Table Information</h2>
                        <svg id="table-info-icon"
                             class="h-5 w-5 text-gray-400 transform transition-transform duration-200" fill="none"
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
                <div id="table-info-content" class="collapsible-content p-6 hidden">

                    <div id="table-info-container">
                        @if($data_source && !empty($data_source['table_name']))
                            @php
                                $table_info = null;
                                foreach ($available_tables as $table) {
                                    if ($table['name'] === $data_source['table_name']) {
                                        $table_info = $table;
                                        break;
                                    }
                                }
                            @endphp
                            @if($table_info)
                                <x-data-source-table-info :table_info="$table_info"/>
                                <x-data-source-column-list :columns="$table_info['columns']"/>
                            @endif
                        @else
                            <div class="text-center py-6 text-gray-500">
                                Select a table to view information
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Join Configuration -->
                <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                     onclick="toggleSection('join-config')">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900">Table Joins</h2>
                        <svg id="join-config-icon"
                             class="h-5 w-5 text-gray-400 transform transition-transform duration-200" fill="none"
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
                <div id="join-config-content" class="collapsible-content p-6 hidden">

                    <div id="join-configuration-container">
                        @php
                            $selected_tables = [];
                            if ($data_source && !empty($data_source['tables'])) {
                                $selected_tables = $data_source['tables'];
                            } elseif ($data_source && !empty($data_source['table_name'])) {
                                $selected_tables = [$data_source['table_name']];
                            }

                            // Populate table_columns for existing data sources
                            $table_columns = [];
                            if (!empty($selected_tables)) {
                                foreach ($selected_tables as $table_name) {
                                    $table_info = data_source_manager::get_table_info($table_name);
                                    if ($table_info) {
                                        $table_columns[$table_name] = $table_info['columns'];
                                    }
                                }
                            }
                        @endphp
                        <x-data-source-join-configuration
                                :selected_tables="$selected_tables"
                                :table_columns="$table_columns"
                                :existing_joins="$data_source['joins'] ?? []"
                        />
                    </div>
                </div>

                <!-- Custom Tables -->
                <div class="bg-white shadow rounded-lg">
                    <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                         onclick="toggleSection('custom-tables')">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Custom Tables & Subqueries</h2>
                            <svg id="custom-tables-icon"
                                 class="h-5 w-5 text-gray-400 transform transition-transform duration-200 {{ empty($data_source['custom_tables'] ?? []) ? '' : 'rotate-180' }}" fill="none"
                                 stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="custom-tables-content" class="collapsible-content p-6 {{ empty($data_source['custom_tables'] ?? []) ? 'hidden' : '' }}">
                        <div class="mb-4">
                            <p class="text-sm text-gray-600 mb-4">
                                Add custom SQL tables, subqueries, or complex joins that can't be created with the standard table selection.
                                Each custom table needs an alias and can be referenced in column selection and filters.
                            </p>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-md font-medium text-gray-900">Custom Tables</h3>
                            <button type="button"
                                    hx-post="{{ APP_ROOT }}/api/data_sources/add_custom_table"
                                    hx-target="#custom-tables-container"
                                    hx-swap="beforeend"
                                    hx-include="form"
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Custom Table
                            </button>
                        </div>

                        <div id="custom-tables-container" class="space-y-4">
                            @if(empty($data_source['custom_tables'] ?? []))
                                <div class="text-center py-6 text-gray-500" id="no-custom-tables-message">
                                    No custom tables configured. Use this section for complex subqueries and custom SQL tables.
                                </div>
                            @else
                                @foreach($data_source['custom_tables'] as $index => $custom_table)
                                    <x-data-source-custom-table-row
                                            :custom_table_index="$index"
                                            :custom_table="$custom_table"
                                    />
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Column Selection -->
                <div class="bg-white shadow rounded-lg">
                    <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                         onclick="toggleSection('column-selection')">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Column Selection</h2>
                            <svg id="column-selection-icon"
                                 class="h-5 w-5 text-gray-400 transform transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="column-selection-content" class="collapsible-content p-6 hidden">
                        <div id="column-selection-container">
                            @php
                                // Convert selected_columns to the expected format for the component
                                $selected_columns_array = [];
                                $selected_columns_data = $data_source['selected_columns'] ?? [];

                                if (is_array($selected_columns_data)) {
                                    // Check if it's in object format (table-grouped) or array format (flat)
                                    if (isset($selected_columns_data[0]) && is_string($selected_columns_data[0])) {
                                        // Already in flat array format like ["table.column", "table.column"]
                                        $selected_columns_array = $selected_columns_data;
                                    } else {
                                        // In object format like {"table": ["col1", "col2"]}
                                        foreach ($selected_columns_data as $table_name => $columns) {
                                            if (is_array($columns)) {
                                                foreach ($columns as $column) {
                                                    $selected_columns_array[] = $table_name . '.' . $column;
                                                }
                                            }
                                        }
                                    }
                                }

                                // Use the column_selection_fragment function to get proper alias support
                                $column_params = [
                                    'selected_tables' => json_encode($selected_tables),
                                    'selected_columns' => json_encode($selected_columns_array),
                                    'joins' => json_encode($data_source['joins'] ?? []),
                                    'table_aliases' => $data_source['table_aliases'] ?? [],
                                    'column_aliases' => $data_source['column_aliases'] ?? [],
                                    'custom_tables' => $data_source['custom_tables'] ?? []
                                ];

                                echo \api\data_sources\column_selection_fragment($column_params);
                            @endphp
                        </div>
                    </div>
                </div>

                <!-- Custom Columns -->
                <div class="bg-white shadow rounded-lg">
                    <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                         onclick="toggleSection('custom-columns')">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Custom Columns</h2>
                            <svg id="custom-columns-icon"
                                 class="h-5 w-5 text-gray-400 transform transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="custom-columns-content" class="collapsible-content p-6 hidden">

                        <x-data-source-custom-columns
                                :custom_columns="$data_source['custom_columns'] ?? []"
                        />
                    </div>
                </div>

                <!-- Data Filters -->
                <div class="bg-white shadow rounded-lg">
                    <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                         onclick="toggleSection('data-filters')">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Data Filters</h2>
                            <svg id="data-filters-icon"
                                 class="h-5 w-5 text-gray-400 transform transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="data-filters-content" class="collapsible-content p-6 hidden">
                        <div class="flex items-center justify-between mb-4">
                            <div></div>
                            <button type="button"
                                    hx-get="{{ APP_ROOT }}/api/data_sources/add_filter_row"
                                    hx-target="#filters-container"
                                    hx-swap="beforeend"
                                    hx-include="form"
                                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M12 4v16m8-8H4"></path>
                                </svg>
                                Add Filter
                            </button>
                        </div>

                        <div id="filters-container" class="space-y-4">
                            @if(empty($data_source['filters'] ?? []))
                                <div class="text-center py-6 text-gray-500" id="no-filters-message">
                                    No filters configured. All data from the table will be included.
                                </div>
                            @else
                                @php
                                    $table_columns = [];
                                    if ($data_source && !empty($data_source['table_name'])) {
                                        foreach ($available_tables as $table) {
                                            if ($table['name'] === $data_source['table_name']) {
                                                $table_columns = $table['columns'];
                                                break;
                                            }
                                        }
                                    }
                                @endphp
                                @foreach($data_source['filters'] as $index => $filter)
                                    <x-data-source-filter-row
                                            :columns="$table_columns"
                                            :filter_operators="$filter_operators"
                                            :filter_index="$index"
                                            :filter="$filter"
                                    />
                                @endforeach
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Grouping -->
                <div class="bg-white shadow rounded-lg">
                    <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                         onclick="toggleSection('grouping')">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Grouping (GROUP BY)</h2>
                            <svg id="grouping-icon"
                                 class="h-5 w-5 text-gray-400 transform transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="grouping-content" class="collapsible-content p-6 hidden">
                        @php
                            // Calculate available columns for grouping
                            $available_columns_for_grouping = [];

                            // Add selected columns
                            if (!empty($data_source['selected_columns'])) {
                                // Handle both formats: flat array ["table.col1", "table.col2"] or nested {"table": ["col1", "col2"]}
                                if (is_array($data_source['selected_columns'])) {
                                    // Check if it's a nested format (table => columns)
                                    $first_key = array_key_first($data_source['selected_columns']);
                                    if (is_string($first_key) && is_array($data_source['selected_columns'][$first_key])) {
                                        // Nested format: {"table1": ["col1", "col2"], "table2": ["col3"]}
                                        foreach ($data_source['selected_columns'] as $table => $columns) {
                                            if (is_array($columns)) {
                                                foreach ($columns as $column) {
                                                    $available_columns_for_grouping[] = $table . '.' . $column;
                                                }
                                            }
                                        }
                                    } else {
                                        // Flat format: ["table1.col1", "table1.col2", "table2.col3"]
                                        foreach ($data_source['selected_columns'] as $column) {
                                            if (is_string($column)) {
                                                $available_columns_for_grouping[] = $column;
                                            }
                                        }
                                    }
                                }
                            }

                            // Add custom columns (only non-aggregate ones)
                            if (!empty($data_source['custom_columns'])) {
                                foreach ($data_source['custom_columns'] as $custom_column) {
                                    if (!empty($custom_column['alias']) && !empty($custom_column['sql'])) {
                                        // Check if the custom column uses aggregate functions
                                        $sql = strtoupper($custom_column['sql']);
                                        $aggregate_functions = ['COUNT(', 'SUM(', 'AVG(', 'MIN(', 'MAX(', 'GROUP_CONCAT('];
                                        $has_aggregate = false;

                                        foreach ($aggregate_functions as $func) {
                                            if (strpos($sql, $func) !== false) {
                                                $has_aggregate = true;
                                                break;
                                            }
                                        }

                                        // Only add non-aggregate custom columns to grouping options
                                        if (!$has_aggregate) {
                                            $available_columns_for_grouping[] = $custom_column['alias'];
                                        }
                                    }
                                }
                            }

                            $available_columns_for_grouping = array_unique($available_columns_for_grouping);
                        @endphp
                        <x-data-source-grouping
                                :grouping="$data_source['grouping'] ?? []"
                                :available_columns="$available_columns_for_grouping"
                        />
                    </div>
                </div>

                <!-- Sorting & Limits -->
                <div class="bg-white shadow rounded-lg">
                    <div class="collapsible-header px-6 py-1 cursor-pointer border-b border-gray-200 hover:bg-gray-50"
                         onclick="toggleSection('sorting-limits')">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-medium text-gray-900">Sorting & Limits</h2>
                            <svg id="sorting-limits-icon"
                                 class="h-5 w-5 text-gray-400 transform transition-transform duration-200"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="sorting-limits-content" class="collapsible-content p-6 hidden">
                        <div class="space-y-6">
                            <!-- Sorting Section -->
                            @php
                                // Calculate available columns for sorting (includes all columns including custom ones)
                                $available_columns_for_sorting = [];

                                // Add selected columns
                                if (!empty($data_source['selected_columns'])) {
                                    // Handle both formats: flat array ["table.col1", "table.col2"] or nested {"table": ["col1", "col2"]}
                                    if (is_array($data_source['selected_columns'])) {
                                        // Check if it's a nested format (table => columns)
                                        $first_key = array_key_first($data_source['selected_columns']);
                                        if (is_string($first_key) && is_array($data_source['selected_columns'][$first_key])) {
                                            // Nested format: {"table1": ["col1", "col2"], "table2": ["col3"]}
                                            foreach ($data_source['selected_columns'] as $table => $columns) {
                                                if (is_array($columns)) {
                                                    foreach ($columns as $column) {
                                                        $available_columns_for_sorting[] = $table . '.' . $column;
                                                    }
                                                }
                                            }
                                        } else {
                                            // Flat format: ["table1.col1", "table1.col2", "table2.col3"]
                                            foreach ($data_source['selected_columns'] as $column) {
                                                if (is_string($column)) {
                                                    $available_columns_for_sorting[] = $column;
                                                }
                                            }
                                        }
                                    }
                                }

                                // Add custom columns (all of them, including aggregates)
                                if (!empty($data_source['custom_columns'])) {
                                    foreach ($data_source['custom_columns'] as $custom_column) {
                                        if (!empty($custom_column['alias'])) {
                                            $available_columns_for_sorting[] = $custom_column['alias'];
                                        }
                                    }
                                }

                                $available_columns_for_sorting = array_unique($available_columns_for_sorting);
                            @endphp
                            <x-data-source-sorting
                                    :sorting="$data_source['sorting'] ?? []"
                                    :available_columns="$available_columns_for_sorting"
                            />

                            <!-- Limits Section -->
                            <x-data-source-limits
                                    :limits="$data_source['limits'] ?? []"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test result container -->

        </div>
        <div id="test-result" class="mt-4"></div>
        <div>
            <!-- Actions -->
            <div class="flex items-center justify-between">
                <div>
                    @if($redirect_url)
                        <a href="{{ $redirect_url }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </a>
                    @endif
                </div>

                <div class="flex items-center space-x-3">
                    <button type="button"
                            hx-post="{{ APP_ROOT }}/api/data_sources/test_table_connection"
                            hx-include="form"
                            hx-target="#test-result"
                            hx-swap="innerHTML"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Test Connection
                    </button>

                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        {{ $mode === 'edit' ? 'Update' : 'Create' }} Data Source
                    </button>
                </div>
            </div>
        </div>

    </div>
</form>
<style>
    .collapsible-content {
        transition: all 0.3s ease-in-out;
    }

    .collapsible-content.hidden {
        display: none;
    }

    .collapsible-header:hover {
        background-color: #f9fafb;
    }
</style>

<script>
    // Collapsible sections functionality
    function toggleSection(sectionId) {
        const content = document.getElementById(sectionId + '-content');
        const icon = document.getElementById(sectionId + '-icon');

        if (content && icon) {
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }
    }

    // Initialize sections - Basic Info expanded by default, others collapsed
    document.addEventListener('DOMContentLoaded', function () {
        // Keep basic info expanded by default
        const basicInfoContent = document.getElementById('basic-info-content');
        const basicInfoIcon = document.getElementById('basic-info-icon');
        if (basicInfoContent && basicInfoIcon) {
            basicInfoContent.classList.remove('hidden');
            basicInfoIcon.style.transform = 'rotate(180deg)';
        }

        // Collapse all other sections by default
        const sections = ['table-info', 'join-config', 'column-selection', 'custom-columns', 'data-filters', 'grouping', 'sorting-limits', 'data-preview', 'query-preview'];
        sections.forEach(sectionId => {
            const content = document.getElementById(sectionId + '-content');
            const icon = document.getElementById(sectionId + '-icon');
            if (content && icon) {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
</script>

<script>
    // Hide "no items" messages when items are added
    document.addEventListener('htmx:afterSwap', function (event) {
        // Hide "no filters" message when filters are added
        if (event.target.id === 'filters-container') {
            const noFiltersMessage = document.getElementById('no-filters-message');
            if (noFiltersMessage && document.querySelectorAll('#filters-container [id^=filter-row-]').length > 0) {
                noFiltersMessage.style.display = 'none';
            }
        }

        // Hide "no tables" message when tables are added
        if (event.target.id === 'selected-tables-container') {
            const noTablesMessage = document.getElementById('no-tables-message');
            if (noTablesMessage && document.querySelectorAll('#selected-tables-container [id^=table-item-]').length > 0) {
                noTablesMessage.style.display = 'none';
            }
        }

        // Hide "no joins" message when joins are added
        if (event.target.id === 'joins-container') {
            const noJoinsMessage = document.getElementById('no-joins-message');
            if (noJoinsMessage && document.querySelectorAll('#joins-container [id^=join-item-]').length > 0) {
                noJoinsMessage.style.display = 'none';
            }
        }

        // Hide "no grouping" message when groups are added
        if (event.target.id === 'grouping-container') {
            const noGroupingMessage = document.getElementById('no-grouping-message');
            if (noGroupingMessage && document.querySelectorAll('#grouping-container [id^=group-item-]').length > 0) {
                noGroupingMessage.style.display = 'none';
            }
        }
    });
</script>


