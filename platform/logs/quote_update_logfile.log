[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 15:54:13
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2e987b9f-f1a5-441e-9e45-1af192062818\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1060665 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-05T15:54:10.958Z\n        )\n\n    [publishedAt] => 2025-09-05T15:54:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 15:54:13] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 15:54:16
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2e987b9f-f1a5-441e-9e45-1af192062818\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1060665 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-05T15:54:10.958Z\n        )\n\n    [publishedAt] => 2025-09-05T15:54:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 15:54:16] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 15:54:22
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 22e0027f-85b0-44e7-a0b9-7125d29bc754\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1060665 status changed to Ordered.\n            [modifiedAt] => 2025-09-05T15:54:19.502Z\n        )\n\n    [publishedAt] => 2025-09-05T15:54:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 15:54:22] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 15:54:24
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 22e0027f-85b0-44e7-a0b9-7125d29bc754\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1060665\n            [transactionId] => fc4dc41c-f182-53c8-82f9-a52f7912aeeb\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1060665 status changed to Ordered.\n            [modifiedAt] => 2025-09-05T15:54:19.502Z\n        )\n\n    [publishedAt] => 2025-09-05T15:54:19.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 15:54:24] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1060665', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1060665', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:32
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 57545a28-f2b3-4dc5-b869-26d5952f8e42\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-987942\n            [transactionId] => c8a9097e-03b7-5c60-9732-68736d04551f\n            [quoteStatus] => Expired\n            [message] => Quote# Q-987942 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:25.082Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:30.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:32] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:33
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 57545a28-f2b3-4dc5-b869-26d5952f8e42\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-987942\n            [transactionId] => c8a9097e-03b7-5c60-9732-68736d04551f\n            [quoteStatus] => Expired\n            [message] => Quote# Q-987942 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:25.082Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:30.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:33] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-987942', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-987942', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:37
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f8f3ff7e-f2ce-427f-a56c-26bb90f033bf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-988019\n            [transactionId] => 100c11a6-6684-59d2-91af-70ab12875176\n            [quoteStatus] => Expired\n            [message] => Quote# Q-988019 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:28.121Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:37
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f8f3ff7e-f2ce-427f-a56c-26bb90f033bf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-988019\n            [transactionId] => 100c11a6-6684-59d2-91af-70ab12875176\n            [quoteStatus] => Expired\n            [message] => Quote# Q-988019 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:28.121Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:37] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-988019', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-988019', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:42
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 491b50e2-81cf-47dc-a396-1454117e2c25\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-986742\n            [transactionId] => b9f568f8-b50f-5168-996b-09f5aabb7b2e\n            [quoteStatus] => Expired\n            [message] => Quote# Q-986742 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:34.019Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:39.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:42
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 491b50e2-81cf-47dc-a396-1454117e2c25\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-986742\n            [transactionId] => b9f568f8-b50f-5168-996b-09f5aabb7b2e\n            [quoteStatus] => Expired\n            [message] => Quote# Q-986742 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:34.019Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:39.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:42] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-986742', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-986742', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:43
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2d506149-b93a-4bfb-b6ab-230d5173052a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995212\n            [transactionId] => dad4b5a5-084e-5c89-aee9-d4b6ae0fb016\n            [quoteStatus] => Expired\n            [message] => Quote# Q-995212 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:34.661Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-05 23:00:43
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2d506149-b93a-4bfb-b6ab-230d5173052a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995212\n            [transactionId] => dad4b5a5-084e-5c89-aee9-d4b6ae0fb016\n            [quoteStatus] => Expired\n            [message] => Quote# Q-995212 status changed to Expired.\n            [modifiedAt] => 2025-09-05T23:00:34.661Z\n        )\n\n    [publishedAt] => 2025-09-05T23:00:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-05 23:00:43] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-06 07:00:34
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 13ab823a-5fbe-42ae-8292-d1a4e5d4455e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-765136\n            [transactionId] => f52b2826-f140-59a7-99c7-48b13c06a3a7\n            [quoteStatus] => Expired\n            [message] => Quote# Q-765136 status changed to Expired.\n            [modifiedAt] => 2025-09-06T07:00:26.246Z\n        )\n\n    [publishedAt] => 2025-09-06T07:00:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-06 07:00:34
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 13ab823a-5fbe-42ae-8292-d1a4e5d4455e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-765136\n            [transactionId] => f52b2826-f140-59a7-99c7-48b13c06a3a7\n            [quoteStatus] => Expired\n            [message] => Quote# Q-765136 status changed to Expired.\n            [modifiedAt] => 2025-09-06T07:00:26.246Z\n        )\n\n    [publishedAt] => 2025-09-06T07:00:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-06 07:00:34] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-765136', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-765136', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-07 23:00:43
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 66337f4b-1ba5-4fb2-974c-794c6f67a5e9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-993161\n            [transactionId] => 2960fbe9-cfb4-5b21-ad7e-9ae62320c5ea\n            [quoteStatus] => Expired\n            [message] => Quote# Q-993161 status changed to Expired.\n            [modifiedAt] => 2025-09-07T23:00:34.681Z\n        )\n\n    [publishedAt] => 2025-09-07T23:00:40.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-07 23:00:43] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-07 23:00:44
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 66337f4b-1ba5-4fb2-974c-794c6f67a5e9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-993161\n            [transactionId] => 2960fbe9-cfb4-5b21-ad7e-9ae62320c5ea\n            [quoteStatus] => Expired\n            [message] => Quote# Q-993161 status changed to Expired.\n            [modifiedAt] => 2025-09-07T23:00:34.681Z\n        )\n\n    [publishedAt] => 2025-09-07T23:00:40.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-07 23:00:44] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-993161', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993161', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-07 23:00:45
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => e1a498c5-3321-4dac-a6be-0a68246430bf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-993269\n            [transactionId] => ad09199f-c5e8-5289-a404-78903022e309\n            [quoteStatus] => Expired\n            [message] => Quote# Q-993269 status changed to Expired.\n            [modifiedAt] => 2025-09-07T23:00:36.327Z\n        )\n\n    [publishedAt] => 2025-09-07T23:00:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-07 23:00:45
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => e1a498c5-3321-4dac-a6be-0a68246430bf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-993269\n            [transactionId] => ad09199f-c5e8-5289-a404-78903022e309\n            [quoteStatus] => Expired\n            [message] => Quote# Q-993269 status changed to Expired.\n            [modifiedAt] => 2025-09-07T23:00:36.327Z\n        )\n\n    [publishedAt] => 2025-09-07T23:00:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-07 23:00:45] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-993269', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-993269', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 08:13:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:13:14
[quote_update] [2025-09-08 08:13:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:13:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 24f9d1b2-75ec-4aab-93f8-c501fb6978c3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1068046 status changed to Draft.\n            [modifiedAt] => 2025-09-08T08:13:11.299Z\n        )\n\n    [publishedAt] => 2025-09-08T08:13:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 08 Sep 2025 08:13:15 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3609\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => dafcb931-b315-475b-9502-af812592c3fc\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => Qkt0RFcLIAMEocQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68be901a-202f38ad30c47a7e7702f4cf\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1068046\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-08T09:13:09+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => TANDEM ARCHITECTS Ltd\n                            [addressLine1] => 5 East Street\n                            [addressLine2] => Drayton\n                            [city] => Langport\n                            [stateProvinceCode] => \n                            [stateProvince] => SOMERSET\n                            [postalCode] => TA10 0LB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Paul\n                            [lastName] => Milton\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-08\n                                    [endDate] => 2026-10-07\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56080812530913\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-07\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-08\n                                                            [startDate] => 2025-10-08\n                                                            [endDate] => 2026-10-07\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1068046\n)\n
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:13:15
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 24f9d1b2-75ec-4aab-93f8-c501fb6978c3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1068046 status changed to Draft.\n            [modifiedAt] => 2025-09-08T08:13:11.299Z\n        )\n\n    [publishedAt] => 2025-09-08T08:13:11.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Draft';\n
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Draft';\n
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Draft';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 08:13:15] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Draft' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Draft';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:13:52
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 89e82df3-826e-410c-96c9-878922774de9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1068046 status changed to Quoted.\n            [modifiedAt] => 2025-09-08T08:13:50.406Z\n        )\n\n    [publishedAt] => 2025-09-08T08:13:50.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 08:13:52] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:13:55
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 89e82df3-826e-410c-96c9-878922774de9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1068046 status changed to Quoted.\n            [modifiedAt] => 2025-09-08T08:13:50.406Z\n        )\n\n    [publishedAt] => 2025-09-08T08:13:50.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 08:13:55] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 08:16:40] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:16:40
[quote_update] [2025-09-08 08:16:40] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:16:40] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 71e894af-91ee-407e-a06f-0633bcbdc93d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068070\n            [transactionId] => ffa65538-36c7-5363-858e-3400a5d81aad\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1068070 status changed to Draft.\n            [modifiedAt] => 2025-09-08T08:16:38.066Z\n        )\n\n    [publishedAt] => 2025-09-08T08:16:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:16:40] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:16:40
[quote_update] [2025-09-08 08:16:40] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:16:40] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 71e894af-91ee-407e-a06f-0633bcbdc93d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068070\n            [transactionId] => ffa65538-36c7-5363-858e-3400a5d81aad\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1068070 status changed to Draft.\n            [modifiedAt] => 2025-09-08T08:16:38.066Z\n        )\n\n    [publishedAt] => 2025-09-08T08:16:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:16:42] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 08 Sep 2025 08:16:42 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3628\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => ce2ac36d-3620-4627-9a49-5259983f3fbe\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QkuUjGxYIAMEguA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68be90e9-2ad6edc83972840d219669f3\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1068070\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-08T09:16:36+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 820\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 820\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 820\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ARDERN & DRUGGAN Ltd\n                            [addressLine1] => The Powerhouse Unit C6 Cooil Road\n                            [addressLine2] => Douglas\n                            [city] => Isle Of Man\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => IM4 2AY\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => kevin\n                            [lastName] => Druggan\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-28\n                                    [endDate] => 2026-08-27\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 2\n                                    [subscription] => Array\n                                        (\n                                            [id] => 72483951997396\n                                            [quantity] => 2\n                                            [endDate] => 2025-08-27\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 820\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 820\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 820\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-08\n                                                            [startDate] => 2025-08-28\n                                                            [endDate] => 2026-08-27\n                                                            [extendedSRP] => 820\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 820\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1068070\n)\n
[quote_update] [2025-09-08 08:16:42] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 08 Sep 2025 08:16:42 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3628\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 8c847c08-d284-4439-87f3-db3c16b3a818\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QkuUkEJQIAMEBdQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68be90e9-27ef0c271e24af3426c50ea7\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1068070\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-08T09:16:36+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 820\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 820\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 820\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ARDERN & DRUGGAN Ltd\n                            [addressLine1] => The Powerhouse Unit C6 Cooil Road\n                            [addressLine2] => Douglas\n                            [city] => Isle Of Man\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => IM4 2AY\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => kevin\n                            [lastName] => Druggan\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-28\n                                    [endDate] => 2026-08-27\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 2\n                                    [subscription] => Array\n                                        (\n                                            [id] => 72483951997396\n                                            [quantity] => 2\n                                            [endDate] => 2025-08-27\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 820\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 820\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 820\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-08\n                                                            [startDate] => 2025-08-28\n                                                            [endDate] => 2026-08-27\n                                                            [extendedSRP] => 820\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 820\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1068070\n)\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:18:03
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 25fe1177-4313-4123-b736-dfaaf55f7e8d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068070\n            [transactionId] => ffa65538-36c7-5363-858e-3400a5d81aad\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1068070 status changed to Quoted.\n            [modifiedAt] => 2025-09-08T08:18:00.639Z\n        )\n\n    [publishedAt] => 2025-09-08T08:18:00.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:18:03
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 25fe1177-4313-4123-b736-dfaaf55f7e8d\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068070\n            [transactionId] => ffa65538-36c7-5363-858e-3400a5d81aad\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1068070 status changed to Quoted.\n            [modifiedAt] => 2025-09-08T08:18:00.639Z\n        )\n\n    [publishedAt] => 2025-09-08T08:18:00.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 08:18:03] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068070', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068070', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:23:41
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 632966c8-073b-41e7-9dc7-2e3e8866e276\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1068046 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-08T08:23:38.356Z\n        )\n\n    [publishedAt] => 2025-09-08T08:23:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:23:41
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 632966c8-073b-41e7-9dc7-2e3e8866e276\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1068046 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-08T08:23:38.356Z\n        )\n\n    [publishedAt] => 2025-09-08T08:23:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 08:23:41] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:23:43
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ebf7be5b-a0c5-471f-9189-12036425d634\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1068046 status changed to Ordered.\n            [modifiedAt] => 2025-09-08T08:23:40.444Z\n        )\n\n    [publishedAt] => 2025-09-08T08:23:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 08:23:43
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ebf7be5b-a0c5-471f-9189-12036425d634\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068046\n            [transactionId] => 63ec4e0b-7306-595e-8971-2f33e1561927\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1068046 status changed to Ordered.\n            [modifiedAt] => 2025-09-08T08:23:40.444Z\n        )\n\n    [publishedAt] => 2025-09-08T08:23:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 08:23:43] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068046', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068046', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 11:11:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:11:04
[quote_update] [2025-09-08 11:11:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:11:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => eec6a563-35a5-4ffe-a0b1-ec53c3644e3e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1068908 status changed to Draft.\n            [modifiedAt] => 2025-09-08T11:11:01.854Z\n        )\n\n    [publishedAt] => 2025-09-08T11:11:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:11:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:11:04
[quote_update] [2025-09-08 11:11:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:11:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => eec6a563-35a5-4ffe-a0b1-ec53c3644e3e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Draft\n            [message] => Quote# Q-1068908 status changed to Draft.\n            [modifiedAt] => 2025-09-08T11:11:01.854Z\n        )\n\n    [publishedAt] => 2025-09-08T11:11:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:11:06] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 08 Sep 2025 11:11:06 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3671\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => ac6cb90c-4dc5-40c1-a9d5-135cc3092c56\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QlH3jEgoIAMEqQQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68beb9c9-26bd301c3d02d9de2684b921\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1068908\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-08T12:11:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => TIM HB Ltd\n                            [addressLine1] => 6 Charterhouse Close\n                            [city] => Cheddar\n                            [stateProvinceCode] => \n                            [stateProvince] => SOMERSET\n                            [postalCode] => BS27 3XT\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Tim\n                            [lastName] => Brigstocke\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-14\n                                    [endDate] => 2026-09-13\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 60008357566478\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-13\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-08\n                                                            [startDate] => 2025-09-14\n                                                            [endDate] => 2026-09-13\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1068908\n)\n
[quote_update] [2025-09-08 11:11:06] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 08 Sep 2025 11:11:06 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3671\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => bd2cb81f-01f4-41ee-9529-452b8592cc18\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QlH3jHR7IAMEohQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68beb9c9-4fbb4dfd0b2f508972e7b13b\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-1068908\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-08T12:11:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => TIM HB Ltd\n                            [addressLine1] => 6 Charterhouse Close\n                            [city] => Cheddar\n                            [stateProvinceCode] => \n                            [stateProvince] => SOMERSET\n                            [postalCode] => BS27 3XT\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Tim\n                            [lastName] => Brigstocke\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-14\n                                    [endDate] => 2026-09-13\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 60008357566478\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-13\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-09-08\n                                                            [startDate] => 2025-09-14\n                                                            [endDate] => 2026-09-13\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-1068908\n)\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:11:39
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 0e2ccd95-8e4d-422e-b8f4-0de6c9468d3b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1068908 status changed to Quoted.\n            [modifiedAt] => 2025-09-08T11:11:36.367Z\n        )\n\n    [publishedAt] => 2025-09-08T11:11:37.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:11:39
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 0e2ccd95-8e4d-422e-b8f4-0de6c9468d3b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-1068908 status changed to Quoted.\n            [modifiedAt] => 2025-09-08T11:11:36.367Z\n        )\n\n    [publishedAt] => 2025-09-08T11:11:37.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 11:11:39] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 11:28:29] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:28:29
[quote_update] [2025-09-08 11:28:29] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:28:29] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8cd39fc7-38fb-4266-a483-548de13aa5f0\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e7279a1d-0c3a-50d7-81e6-fb25cc2b410a\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-08T11:28:27.303Z\n        )\n\n    [publishedAt] => 2025-09-08T11:28:27.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:28:29] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:28:29
[quote_update] [2025-09-08 11:28:29] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:28:29] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8cd39fc7-38fb-4266-a483-548de13aa5f0\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e7279a1d-0c3a-50d7-81e6-fb25cc2b410a\n            [quoteStatus] => Draft\n            [message] => Quote# ********* status changed to Draft.\n            [modifiedAt] => 2025-09-08T11:28:27.303Z\n        )\n\n    [publishedAt] => 2025-09-08T11:28:27.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:28:31] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 08 Sep 2025 11:28:31 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1746\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 8087e919-ed67-43b8-a69f-afb48bb8ff51\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QlKa3FmDoAMEECA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68bebdde-731fba1d0c49a34144cf90eb\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-08T12:28:25+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Alu-Fix (Facades) Ltd\n                            [addressLine1] => Unit 3 Bellsbridge Office Park 100c\n                            [addressLine2] => Ladas Drive\n                            [city] => Belfast\n                            [stateProvinceCode] => \n                            [stateProvince] => County Antrim\n                            [postalCode] => BT6 9FH\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Ben\n                            [lastName] => Wilson\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-08 11:28:31] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 08 Sep 2025 11:28:31 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1647\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 19f5e28c-b6f4-4614-a086-22e66f678c18\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => QlKa3FtZIAMEfTg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-68bebdde-62f45b872f94bc635ecd580c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => *********\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-09-08T12:28:25+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Alu-Fix (Facades) Ltd\n                            [addressLine1] => Unit 3 Bellsbridge Office Park 100c\n                            [addressLine2] => Ladas Drive\n                            [city] => Belfast\n                            [stateProvinceCode] => \n                            [stateProvince] => County Antrim\n                            [postalCode] => BT6 9FH\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Ben\n                            [lastName] => Wilson\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=*********\n)\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:29:57
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1d56e980-8b67-41c1-be62-3f253843cfda\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e7279a1d-0c3a-50d7-81e6-fb25cc2b410a\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-08T11:29:55.049Z\n        )\n\n    [publishedAt] => 2025-09-08T11:29:55.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:29:57
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 1d56e980-8b67-41c1-be62-3f253843cfda\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => *********\n            [transactionId] => e7279a1d-0c3a-50d7-81e6-fb25cc2b410a\n            [quoteStatus] => Quoted\n            [message] => Quote# ********* status changed to Quoted.\n            [modifiedAt] => 2025-09-08T11:29:55.049Z\n        )\n\n    [publishedAt] => 2025-09-08T11:29:55.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 11:29:57] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = '*********', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = '*********', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:40:34
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 89afb3b0-7687-45c9-beb7-bba4660a51e5\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1051522\n            [transactionId] => a98bcd41-bf1e-5ef2-8c15-ac716fa6e3c9\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1051522 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-08T11:40:32.286Z\n        )\n\n    [publishedAt] => 2025-09-08T11:40:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:40:34
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 89afb3b0-7687-45c9-beb7-bba4660a51e5\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1051522\n            [transactionId] => a98bcd41-bf1e-5ef2-8c15-ac716fa6e3c9\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1051522 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-08T11:40:32.286Z\n        )\n\n    [publishedAt] => 2025-09-08T11:40:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 11:40:34] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1051522', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1051522', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:52:34
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 24a14678-188e-43b9-8aeb-031b524948ea\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1068908 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-08T11:52:32.102Z\n        )\n\n    [publishedAt] => 2025-09-08T11:52:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 11:52:34] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:52:35
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 24a14678-188e-43b9-8aeb-031b524948ea\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-1068908 status changed to Order Submitted.\n            [modifiedAt] => 2025-09-08T11:52:32.102Z\n        )\n\n    [publishedAt] => 2025-09-08T11:52:32.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 11:52:35] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:52:36
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:515] Starting quote change processing at 2025-09-08 11:52:36
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c0b514c1-7a52-45da-bb51-2e427438d481\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1068908 status changed to Ordered.\n            [modifiedAt] => 2025-09-08T11:52:33.631Z\n        )\n\n    [publishedAt] => 2025-09-08T11:52:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => c0b514c1-7a52-45da-bb51-2e427438d481\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-1068908\n            [transactionId] => 102119e3-52a1-5761-965c-78dbe9509f28\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-1068908 status changed to Ordered.\n            [modifiedAt] => 2025-09-08T11:52:33.631Z\n        )\n\n    [publishedAt] => 2025-09-08T11:52:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-09-08 11:52:36] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-1068908', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-1068908', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
