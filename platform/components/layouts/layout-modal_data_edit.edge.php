@props([
    'title' => 'Data Display',
    'description' => '',
    'items' => [],
    'layout' => [],
    'content' => [],
    'class' => '',
    'root_key' => 'data',
    'endpoint' => '',
    'button_label' => 'Save',
    'button_cancel_label' => 'Cancel',
    'field_types' => [], // Optional mapping of field names to specific field types
    'field_options' => [] // Optional options for select fields
])
@php
    print_rr($layout,'layout');

    // Helper function to determine field type based on variable type and name
    function getFieldType($value, $fieldName, $fieldTypes = []) {
        // Check if there's a specific type defined for this field
        if (isset($fieldTypes[$fieldName])) {
            return $fieldTypes[$fieldName];
        }

        // Determine type based on value
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_numeric($value)) {
            return 'number';
        } elseif (is_string($value)) {
            // Check for common field name patterns
            $lowercaseName = strtolower($fieldName);
            if (str_contains($lowercaseName, 'date') || str_contains($lowercaseName, 'time')) {
                return 'date';
            } elseif (str_contains($lowercaseName, 'email')) {
                return 'email';
            } elseif (str_contains($lowercaseName, 'password')) {
                return 'password';
            } elseif (str_contains($lowercaseName, 'color')) {
                return 'color';
            } elseif (str_contains($lowercaseName, 'url') || str_contains($lowercaseName, 'link')) {
                return 'url';
            } elseif (strlen($value) > 100) {
                return 'textarea';
            }
            return 'text';
        }

        // Default to text input
        return 'text';
    }

    // Helper function to render the appropriate field component
    function renderField($fieldName, $value, $nameAttr, $fieldTypes = [], $fieldOptions = []) {
        $fieldType = getFieldType($value, $fieldName, $fieldTypes);

        switch ($fieldType) {
            case 'boolean':
                // For boolean values, create a select with Yes/No options
                $options = ['1' => 'Yes', '0' => 'No'];
                $selected = $value ? '1' : '0';
                return Edge::render('forms-select', [
                    'label' => $fieldName,
                    'options' => ['1' => 'Yes', '0' => 'No'],
                    'selected' => $selected,
                    'name' => $nameAttr
                ]);

            case 'select':
                // For select fields, use provided options or default to empty array
                if (isset($fieldOptions[$fieldName])) {
                    return Edge::render('forms-select', [
                        'label' => $fieldName,
                        'options' => $fieldOptions[$fieldName],
                        'selected' => $value,
                        'name' => $nameAttr
                    ]);
                } else {
                    // Fallback to text input if no options provided
                    return Edge::render('forms-input', [
                        'label' => $fieldName,
                        'value' => $value,
                        'name' => $nameAttr
                    ]);
                }

            case 'textarea':
                return Edge::render('forms-textarea', [
                    'label' => $fieldName,
                    'content' => $value,
                    'name' => $nameAttr
                ]);

            case 'number':
                return Edge::render('forms-input', [
                    'label' => $fieldName,
                    'value' => $value,
                    'name' => $nameAttr,
                    'type' => 'number'
                ]);

            case 'date':
                return Edge::render('forms-input', [
                    'label' => $fieldName,
                    'value' => $value,
                    'name' => $nameAttr,
                    'type' => 'date'
                ]);

            case 'email':
                return Edge::render('forms-input', [
                    'label' => $fieldName,
                    'value' => $value,
                    'name' => $nameAttr,
                    'type' => 'email'
                ]);

            case 'password':
                return Edge::render('forms-input', [
                    'label' => $fieldName,
                    'value' => $value,
                    'name' => $nameAttr,
                    'type' => 'password'
                ]);

            case 'color':
                return Edge::render('forms-input', [
                    'label' => $fieldName,
                    'value' => $value,
                    'name' => $nameAttr,
                    'type' => 'color'
                ]);

            case 'url':
                return Edge::render('forms-input', [
                    'label' => $fieldName,
                    'value' => $value,
                    'name' => $nameAttr,
                    'type' => 'url'
                ]);

            default:
                return Edge::render('forms-input', [
                    'label' => $fieldName,
                    'value' => $value,
                    'name' => $nameAttr
                ]);
        }
    }
@endphp

<div class="bg-gray-100">
    <x-forms-form :hx-post="$endpoint">
        <x-layout-card
                :label="$base_key"
                class_suffix=""
                class=""
        >
            <div class="flex flex-col gap-1">
                @foreach ($layout as $base_key => $base_column)
                    <div class="flex flex-col gap-4">
                        @php
                            print_rr($base_column,'column');
                        @endphp
                        @if (!is_array($base_column))
                            <div class="px-1 py-1 h-min grid grid-cols-2 sm:gap-4 sm:px-0">
                                {!! renderField($base_key, $base_column, $root_key. '['. $base_key . ']', $field_types, $field_options) !!}
                            </div>
                        @else
                            @foreach ($base_column as $colkey => $column)
                                @if (!is_array($column))
                                    <x-data
                                    <div class="px-1 py-1 h-min grid grid-cols-2 sm:gap-4 sm:px-0">
                                        {!! renderField($colkey, $column, $root_key. '['. $base_key . ']' . '[' . $colkey . ']', $field_types, $field_options) !!}
                                    </div>
                                @else
                                    {!! Edge::render('layout-box', ['label' => $base_key], 1) !!}
                                    @foreach ($column as $subKey => $subColumn)
                                        <div class="px-1 py-1 h-min grid grid-cols-2 sm:gap-4 sm:px-0">
                                            {!! renderField($subKey, $subColumn, $root_key. '['. $base_key . ']' . '[' . $colkey .']' . '[' . $subKey . ']', $field_types, $field_options) !!}
                                        </div>
                                    @endforeach
                                    {!! Edge::render('layout-box', [], 2) !!}
                                @endif
                            @endforeach
                        @endif
                    </div>
                @endforeach
            </div>
            <div class="flex justify-end gap-x-3 pt-4 grid grid-cols-2">
                <div class="flex gap-x-3 pt-4">
                    <x-forms-button x-on:click="showModal = false" type="button" :label="{{ $button_cancel_label }}"
                                    variant="primary"/>
                </div>
                <div class="flex justify-end gap-x-3 pt-4">
                    <x-forms-button type="submit" :label="{{ $button_label }}" variant="primary"/>
                </div>
            </div>
        </x-layout-card>
    </x-forms-form>
</div>