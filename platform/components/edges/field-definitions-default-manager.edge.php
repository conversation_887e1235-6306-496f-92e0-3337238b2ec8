@props([
    'name' => 'field-definitions-default-manager',
    'description' => 'Displays the available field definitions with category tabs and field cards. Can be used as an OOB swap target when field definitions are updated.',
    'preview_data' => [],
])

@use system\unified_field_definitions
@print_rr($active_category,'active_category')

<!-- Default Data Table Preview -->
<div class="mt-8 border-t border-gray-200 pt-8">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h3 class="text-lg font-medium text-gray-900">Default Data Table Preview</h3>
            <p class="text-sm text-gray-600 mt-1">
                Live preview showing fields marked "Show by default in data tables". This configuration will be applied to new CSV imports and data source tables.
            </p>
        </div>
        <div class="flex items-center space-x-2">
            <button type="button"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    hx-post="{{ APP_ROOT }}/api/data_table/data_table_filter"
                    hx-vals='{"table_name": "unified_field_definitions_default_preview", "callback": "get_default_field_preview_data"}'
                    hx-target=".data_table"
                    hx-swap="outerHTML">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh Preview
            </button>
            <button type="button"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    hx-post="{{ APP_ROOT }}/api/unified_field_definitions/save_table_config"
                    hx-vals='{"table_name": "unified_field_definitions_default_preview"}'
                    hx-target="#save-status"
                    hx-swap="innerHTML">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Save Current Configuration as Default
            </button>
        </div>
    </div>

    <div id="save-status" class="mb-4"></div>

  
    @if(!empty($preview_data['columns']))
        <x-data-table
                table_name="unified_field_definitions_default_preview"
                :items="$preview_data['items']"
                :columns="$preview_data['columns']"
                callback="get_default_field_preview_data"
                :available_fields="$preview_data['fields']"
                current_data_source_type="unified_field_definitions_default"
                :current_data_source_id="null"
                show_search="false"
                show_pagination="false"
                empty_message="Configure fields above with 'Show by default' to see preview data here."
        />

        <!-- Configuration Info -->
        <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div class="text-sm text-blue-800">
                <p><strong>Configuration:</strong> unified_field_definitions_default</p>
                <p><strong>Fields shown:</strong> {{ count($preview_data['columns']) }} fields marked as "Show by default"</p>
                <p><strong>Sample data:</strong> {{ count($preview_data['items']) }} preview rows</p>
                <p class="mt-2"><strong>Fields included:</strong>
                    @foreach($preview_data['columns'] as $col)
                        <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-1 mb-1">
                                        {{ $col['label'] }} ({{ $col['category'] }})
                                    </span>
                    @endforeach
                </p>
            </div>
        </div>
    @else
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
            <svg class="w-12 h-12 text-yellow-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <h3 class="text-lg font-medium text-yellow-800 mb-2">No Default Fields Configured</h3>
            <p class="text-yellow-700 mb-4">
                {{ $preview_data['message'] ?: 'No fields are currently set to "Show by default". Edit field definitions above to mark fields as default display fields.' }}
            </p>
            <p class="text-sm text-yellow-600">
                To configure default fields, edit any field definition above and check "Show by default in data tables" in the Display Configuration section.
            </p>
        </div>
    @endif
</div>
