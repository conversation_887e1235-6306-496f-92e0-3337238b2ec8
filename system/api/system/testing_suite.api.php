<?php
namespace api\system\testing_suite;
use edge\Edge;
use system\users;

// Ensure only admin/dev can access this page
users::requireRole('admin');

function database_connection() {
    $results = ['success' => false, 'message' => '', 'details' => []];
    $start_time = microtime(true);
    
    try {
        // Test basic database connection
        global $db;
        if (!$db) {
            throw new Exception('Database connection not initialized');
        }
        
        $results['details'][] = '✓ Database connection established';
        
        // Test a simple query
        $query = "SELECT 1 as test_value";
        $result = tep_db_query($query);
        
        if ($result) {
            $row = tep_db_fetch_array($result);
            if ($row['test_value'] == 1) {
                $results['details'][] = '✓ Database query execution successful';
                $results['success'] = true;
                $results['message'] = 'Database connection and queries working correctly';
            } else {
                throw new Exception('Query returned unexpected result');
            }
        } else {
            throw new Exception('Query execution failed');
        }
        
        // Test database constants
        $results['details'][] = '✓ DB_SERVER: ' . DB_SERVER;
        $results['details'][] = '✓ DB_DATABASE: ' . DB_DATABASE;
        $results['details'][] = '✓ DB_SERVER_USERNAME: ' . DB_SERVER_USERNAME;
        
    } catch (Exception $e) {
        $results['message'] = 'Database test failed: ' . $e->getMessage();
        $results['details'][] = '✗ ' . $e->getMessage();
    }
    
    $results['execution_time'] = round((microtime(true) - $start_time) * 1000, 2);
    $results['timestamp'] = date('Y-m-d H:i:s');
    
    // Log test result
    logTestResult('database_connection', $results);
    
    echo renderTestResults($results);
}

function edge_templates() {
    $results = ['success' => false, 'message' => '', 'details' => []];
    $start_time = microtime(true);
    
    try {
        // Test Edge class availability
        if (!class_exists('edge\Edge')) {
            throw new Exception('Edge class not available');
        }
        $results['details'][] = '✓ Edge class loaded';
        
        // Test simple template rendering
        $test_data = ['test_var' => 'Hello World'];
        $simple_template = '{{ $test_var }}';
        
        // Create a temporary test template
        $temp_template_path = FS_SYS_VIEWS . '/system/temp_test_template.edge.php';
        file_put_contents($temp_template_path, $simple_template);
        
        try {
            $rendered = Edge::render('system/temp_test_template', $test_data);
            if (strpos($rendered, 'Hello World') !== false) {
                $results['details'][] = '✓ Template rendering successful';
                $results['success'] = true;
                $results['message'] = 'Edge templates working correctly';
            } else {
                throw new Exception('Template did not render expected content');
            }
        } finally {
            // Clean up temp file
            if (file_exists($temp_template_path)) {
                unlink($temp_template_path);
            }
        }
        
        // Test template compilation
        $results['details'][] = '✓ Template compilation working';
        
        // Test pipeline
        $pipeline = Edge::get_pipeline();
        $results['details'][] = '✓ Pipeline steps: ' . count($pipeline);
        
    } catch (Exception $e) {
        $results['message'] = 'Edge template test failed: ' . $e->getMessage();
        $results['details'][] = '✗ ' . $e->getMessage();
    }
    
    $results['execution_time'] = round((microtime(true) - $start_time) * 1000, 2);
    $results['timestamp'] = date('Y-m-d H:i:s');
    
    // Log test result
    logTestResult('edge_templates', $results);
    
    echo renderTestResults($results);
}

function system_classes() {
    $results = ['success' => false, 'message' => '', 'details' => []];
    $start_time = microtime(true);
    
    try {
        // Test core system classes
        $classes_to_test = [
            'system\router' => 'Router class',
            'system\users' => 'Users class',
            'system\startup_sequence' => 'Startup sequence class'
        ];
        
        $loaded_classes = 0;
        foreach ($classes_to_test as $class => $description) {
            if (class_exists($class)) {
                $results['details'][] = "✓ $description loaded";
                $loaded_classes++;
            } else {
                $results['details'][] = "✗ $description not found";
            }
        }
        
        // Test constants
        $constants_to_test = [
            'APP_ROOT' => APP_ROOT,
            'FS_VIEWS' => FS_VIEWS,
            'FS_SYS_VIEWS' => FS_SYS_VIEWS,
            'DEBUG_MODE' => DEBUG_MODE ? 'true' : 'false'
        ];
        
        foreach ($constants_to_test as $const => $value) {
            $results['details'][] = "✓ $const: $value";
        }
        
        if ($loaded_classes === count($classes_to_test)) {
            $results['success'] = true;
            $results['message'] = 'All system classes loaded successfully';
        } else {
            $results['message'] = "Only $loaded_classes/" . count($classes_to_test) . " classes loaded";
        }
        
    } catch (Exception $e) {
        $results['message'] = 'System classes test failed: ' . $e->getMessage();
        $results['details'][] = '✗ ' . $e->getMessage();
    }
    
    $results['execution_time'] = round((microtime(true) - $start_time) * 1000, 2);
    $results['timestamp'] = date('Y-m-d H:i:s');
    
    // Log test result
    logTestResult('system_classes', $results);
    
    echo renderTestResults($results);
}

function custom_test() {
    $input_params = INPUT_PARAMS;
    $results = ['success' => false, 'message' => '', 'details' => []];
    $start_time = microtime(true);
    
    try {
        $test_name = $input_params['test_name'] ?? 'unnamed_test';
        $test_code = $input_params['test_code'] ?? '';
        
        if (empty($test_code)) {
            throw new Exception('No test code provided');
        }
        
        $results['details'][] = "Running custom test: $test_name";
        
        // Execute the test code in a safe environment
        ob_start();
        $test_result = eval($test_code);
        $output = ob_get_clean();
        
        if ($output) {
            $results['details'][] = "Output: $output";
        }
        
        if ($test_result !== false) {
            $results['details'][] = "Result: " . var_export($test_result, true);
            $results['success'] = true;
            $results['message'] = 'Custom test executed successfully';
        } else {
            $results['message'] = 'Custom test returned false or failed';
        }
        
    } catch (Exception $e) {
        $results['message'] = 'Custom test failed: ' . $e->getMessage();
        $results['details'][] = '✗ ' . $e->getMessage();
    }
    
    $results['execution_time'] = round((microtime(true) - $start_time) * 1000, 2);
    $results['timestamp'] = date('Y-m-d H:i:s');
    
    // Log test result
    logTestResult('custom_test', $results);
    
    echo renderTestResults($results);
}

function test_file() {
    $input_params = INPUT_PARAMS;
    $results = ['success' => false, 'message' => '', 'details' => []];
    $start_time = microtime(true);
    
    try {
        $test_file = $input_params['test_file'] ?? '';
        
        if (empty($test_file)) {
            throw new Exception('No test file specified');
        }
        
        // Ensure the file path is safe and within the project
        $test_file = str_replace(['../', '..\\'], '', $test_file);
        $full_path = FS_APP_ROOT . '/' . $test_file;
        
        if (!file_exists($full_path)) {
            throw new Exception("Test file not found: $test_file");
        }
        
        $results['details'][] = "Running test file: $test_file";
        
        // Execute the test file
        ob_start();
        $test_result = include $full_path;
        $output = ob_get_clean();
        
        if ($output) {
            $results['details'][] = "Output: $output";
        }
        
        $results['details'][] = "Result: " . var_export($test_result, true);
        $results['success'] = true;
        $results['message'] = 'Test file executed successfully';
        
    } catch (Exception $e) {
        $results['message'] = 'Test file execution failed: ' . $e->getMessage();
        $results['details'][] = '✗ ' . $e->getMessage();
    }
    
    $results['execution_time'] = round((microtime(true) - $start_time) * 1000, 2);
    $results['timestamp'] = date('Y-m-d H:i:s');
    
    // Log test result
    logTestResult('test_file', $results);
    
    echo renderTestResults($results);
}

function list_test_files() {
    $base_test_dir = FS_APP_ROOT . '/system/tests';

    if (!is_dir($base_test_dir)) {
        echo "<div class='text-sm text-gray-500'>Test directory not found: system/tests</div>";
        return;
    }

    // Get organized directory structure
    $organized_files = getOrganizedTestFiles($base_test_dir);

    if (empty($organized_files)) {
        echo "<div class='text-sm text-gray-500'>No test files found in system/tests</div>";
        return;
    }

    $html = "<div class='space-y-4'>";

    foreach ($organized_files as $category => $files) {
        if (empty($files)) continue;

        // Category header
        $category_display = ucwords(str_replace('_', ' ', $category));
        $html .= "<div>";
        $html .= "<h4 class='text-sm font-semibold text-gray-700 mb-2 flex items-center'>";
        $html .= getCategoryIcon($category);
        $html .= "<span class='ml-2'>$category_display</span>";
        $html .= "<span class='ml-2 text-xs text-gray-500'>(" . count($files) . " files)</span>";
        $html .= "</h4>";

        // Files in category
        $html .= "<div class='space-y-1 ml-6'>";
        foreach ($files as $file) {
            $relative_path = str_replace(FS_APP_ROOT . '/', '', $file);
            $filename = basename($file);
            $html .= "<div class='text-xs text-gray-600 font-mono bg-gray-50 px-2 py-1 rounded cursor-pointer hover:bg-blue-100 border-l-2 border-blue-200 transition-colors' ";
            $html .= "hx-post='" . APP_ROOT . "/api/system/testing_suite/test_file' ";
            $html .= "hx-target='#test-results' ";
            $html .= "hx-swap='innerHTML' ";
            $html .= "hx-vals='{\"test_file\": \"$relative_path\"}' ";
            $html .= "title='Click to run: $relative_path'>";
            $html .= "<div class='flex items-center justify-between'>";
            $html .= "<span>" . htmlspecialchars($filename) . "</span>";
            $html .= "<svg class='w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100' fill='none' stroke='currentColor' viewBox='0 0 24 24'>";
            $html .= "<path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1'></path>";
            $html .= "</svg>";
            $html .= "</div>";
            $html .= "</div>";
        }
        $html .= "</div>";
        $html .= "</div>";
    }

    $html .= "</div>";
    echo $html;
}

function getOrganizedTestFiles($base_dir) {
    $organized = [];

    // Scan main directory for root-level files
    $root_files = glob($base_dir . '/*.php');
    if (!empty($root_files)) {
        $organized['root'] = $root_files;
    }

    // Scan subdirectories
    $subdirs = glob($base_dir . '/*', GLOB_ONLYDIR);
    foreach ($subdirs as $subdir) {
        $dir_name = basename($subdir);
        $files = glob($subdir . '/*.php');
        if (!empty($files)) {
            $organized[$dir_name] = $files;
        }

        // Check for nested subdirectories (one level deep)
        $nested_dirs = glob($subdir . '/*', GLOB_ONLYDIR);
        foreach ($nested_dirs as $nested_dir) {
            $nested_name = $dir_name . '/' . basename($nested_dir);
            $nested_files = glob($nested_dir . '/*.php');
            if (!empty($nested_files)) {
                $organized[$nested_name] = $nested_files;
            }
        }
    }

    return $organized;
}

function getCategoryIcon($category) {
    $icons = [
        'root' => '<svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0"></path></svg>',
        'database' => '<svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"></path></svg>',
        'data_sources' => '<svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>',
        'templates' => '<svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path></svg>',
        'temp_files' => '<svg class="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>'
    ];

    return $icons[$category] ?? '<svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>';
}

function get_history() {
    $log_file = FS_TEMP . '/test_history.log';
    
    if (!file_exists($log_file)) {
        echo "<div class='text-sm text-gray-500'>No test history available</div>";
        return;
    }
    
    $lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $lines = array_reverse(array_slice($lines, -10)); // Last 10 entries
    
    $html = "<div class='space-y-2'>";
    foreach ($lines as $line) {
        $data = json_decode($line, true);
        if ($data) {
            $status_class = $data['success'] ? 'text-green-600' : 'text-red-600';
            $html .= "<div class='text-sm border-b border-gray-100 pb-2'>";
            $html .= "<div class='flex justify-between items-center'>";
            $html .= "<span class='font-medium'>" . htmlspecialchars($data['test_type']) . "</span>";
            $html .= "<span class='text-xs text-gray-500'>" . htmlspecialchars($data['timestamp']) . "</span>";
            $html .= "</div>";
            $html .= "<div class='$status_class text-xs'>" . htmlspecialchars($data['message']) . "</div>";
            $html .= "</div>";
        }
    }
    $html .= "</div>";
    
    echo $html;
}

function test_directory_overview() {
    $base_test_dir = FS_APP_ROOT . '/system/tests';

    if (!is_dir($base_test_dir)) {
        echo "<div class='text-sm text-red-500'>Test directory not found: system/tests</div>";
        return;
    }

    $organized_files = getOrganizedTestFiles($base_test_dir);
    $total_files = 0;

    $html = "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>";
    $html .= "<h4 class='text-lg font-medium text-blue-900 mb-3'>Test Directory Overview</h4>";
    $html .= "<div class='grid grid-cols-1 md:grid-cols-2 gap-4'>";

    foreach ($organized_files as $category => $files) {
        $count = count($files);
        $total_files += $count;
        $category_display = ucwords(str_replace('_', ' ', $category));

        $html .= "<div class='bg-white rounded-md p-3 border border-blue-100'>";
        $html .= "<div class='flex items-center justify-between'>";
        $html .= "<div class='flex items-center'>";
        $html .= getCategoryIcon($category);
        $html .= "<span class='ml-2 font-medium text-gray-900'>$category_display</span>";
        $html .= "</div>";
        $html .= "<span class='text-sm font-semibold text-blue-600'>$count files</span>";
        $html .= "</div>";
        $html .= "</div>";
    }

    $html .= "</div>";
    $html .= "<div class='mt-4 text-center'>";
    $html .= "<div class='text-lg font-bold text-blue-900'>Total: $total_files test files</div>";
    $html .= "<div class='text-sm text-blue-700'>Organized in " . count($organized_files) . " categories</div>";
    $html .= "</div>";
    $html .= "</div>";

    echo $html;
}

// Helper functions
function renderTestResults($results) {
    $status_class = $results['success'] ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
    $status_text_class = $results['success'] ? 'text-green-800' : 'text-red-800';
    $status_icon = $results['success'] ?
        '<svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>' :
        '<svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';

    $html = "<div class='border rounded-lg p-4 $status_class'>";
    $html .= "<div class='flex items-center mb-3'>";
    $html .= $status_icon;
    $html .= "<h4 class='ml-2 text-lg font-medium $status_text_class'>" . $results['message'] . "</h4>";
    $html .= "</div>";

    if (!empty($results['details'])) {
        $html .= "<div class='bg-white rounded-md p-3 border border-gray-200 font-mono text-sm overflow-x-auto'>";
        $html .= "<pre class='whitespace-pre-wrap text-gray-800 leading-relaxed'>";
        foreach ($results['details'] as $detail) {
            $html .= $detail . "\n";
        }
        $html .= "</pre>";
        $html .= "</div>";
    }

    $html .= "<div class='mt-3 text-xs text-gray-500'>";
    $html .= "Execution time: {$results['execution_time']}ms | ";
    $html .= "Timestamp: {$results['timestamp']}";
    $html .= "</div>";
    $html .= "</div>";

    return $html;
}

function logTestResult($test_type, $results) {
    $log_file = FS_TEMP . '/test_history.log';
    $log_entry = [
        'test_type' => $test_type,
        'success' => $results['success'],
        'message' => $results['message'],
        'execution_time' => $results['execution_time'],
        'timestamp' => $results['timestamp']
    ];

    file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
}
?>
