{"search_columns": ["subs.subscriptionReferenceNumber", "subs.offeringName", "subs.subscriptionid", "soldto.account_csn", "soldto.name", "solpro.account_csn", "solpro.name", "solpro.address1", "solpro.address2", "solpro.address3", "solpro.city", "solpro.state_province", "solpro.postal_code", "solpro.country", "solpro.state_province_code", "solpro.country_code", "resell.account_csn", "resell.name", "endcust.account_csn", "endcust.name", "endcust.address1", "endcust.address2", "endcust.address3", "endcust.city", "endcust.state_province", "endcust.postal_code", "endcust.country", "endcust.primary_admin_first_name", "endcust.primary_admin_last_name", "endcust.primary_admin_email", "endcust.team_id", "endcust.team_name", "endcust.first_name", "endcust.last_name", "endcust.email"], "order_by": "subs_enddatediff", "limit": 300}