@props([
    'title' => 'form textarea control',
    'description' => 'Basic form select',
    'content' => '', // An array of items.
    'label' => null,
    'id' => '',
    'name' => '',
    'rows' => 4,
    'placeholder' => '',
    'class_suffix' => '',
])
<div>
    @if ($label)
        <label for="{{ $name }}" class="block text-sm/6 font-medium text-gray-900">{{ $label }}</label>
    @endif
    <textarea
            rows="{{ $rows }}"
            name="{{ $name }}"
            id="{{ $id }}"
            class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 {{ $class_suffix }}"
            @if ($placeholder) placeholder="{{ $placeholder }}" @endif
            {{ $extra_attributes }}
    >{{ $content }}</textarea>
</div>

