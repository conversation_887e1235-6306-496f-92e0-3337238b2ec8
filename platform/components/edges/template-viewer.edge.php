@props([
    'title' => 'Template Viewer',
    'description' => 'View and interact with templates',
    'default_template' => '', // Default template to display
])

<div x-data="{
    selectedTemplate: '{{ $default_template }}',
    templateTitle: '',
    templateDescription: '',
    templateData: {}
}" class="space-y-6">
    <!-- Template Selection -->
    <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900">Select a Template</h3>
            <div class="mt-2 max-w-xl text-sm text-gray-500">
                <p>Choose a template from the available options to view and interact with it.</p>
            </div>
            <div class="mt-5">
                <x-template-selector 
                    name="template"
                    label="Template"
                    :selected="$default_template ? ['id' => $default_template] : null"
                    @template-selected="
                        selectedTemplate = $event.detail.template;
                        templateTitle = $event.detail.title;
                        templateDescription = $event.detail.description;
                    "
                />
            </div>
        </div>
    </div>
    
    <!-- Template Display -->
    <div x-show="selectedTemplate" class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900" x-text="templateTitle || 'Template Preview'"></h3>
            <div class="mt-2 max-w-xl text-sm text-gray-500">
                <p x-text="templateDescription || 'Preview of the selected template'"></p>
            </div>
            <div class="mt-5">
                <div x-show="selectedTemplate === 'datatable-csv'">
                    <x-templates-csv-uploader />
                </div>
                <div x-show="selectedTemplate && selectedTemplate !== 'datatable-csv' && selectedTemplate !== 'csv-uploader'">
                    <x-template-container :template="''" x-bind:template="selectedTemplate" :template_data="[]" />
                </div>
            </div>
        </div>
    </div>
</div>
