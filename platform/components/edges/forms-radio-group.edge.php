@props([
    'title' => 'form radio group control',
    'description' => 'Group of radio buttons',
    'name' => '',
    'options' => [], // Array of options with value, label, and checked properties
    'layout' => 'horizontal', // horizontal or vertical
    'class_suffix' => '',
    'extra_attributes' => '',
])

<div class="{{ $layout === 'vertical' ? 'space-y-4' : 'flex items-center space-x-6' }}">
    @foreach($options as $option)
        <x-forms-radio
            name="{{ $name }}"
            id="{{ $name }}_{{ $option['value'] }}"
            value="{{ $option['value'] }}"
            label="{{ $option['label'] }}"
            checked="{{ $option['checked'] ?? false }}"
            {{ $extra_attributes }}
        />
    @endforeach
</div>
