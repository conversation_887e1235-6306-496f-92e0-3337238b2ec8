@props([
    'title' => 'Error',
    'message' => 'An error occurred',
    'type' => 'error', // 'error', 'warning', 'info'
    'show_back_button' => true,
    'back_url' => null
])

@php
$type_classes = [
    'error' => 'bg-red-50 border-red-200 text-red-700',
    'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-700', 
    'info' => 'bg-blue-50 border-blue-200 text-blue-700'
];

$icon_classes = [
    'error' => 'text-red-600',
    'warning' => 'text-yellow-600',
    'info' => 'text-blue-600'
];

$button_classes = [
    'error' => 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
    'warning' => 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
    'info' => 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
];

$container_class = $type_classes[$type] ?? $type_classes['error'];
$icon_class = $icon_classes[$type] ?? $icon_classes['error'];
$button_class = $button_classes[$type] ?? $button_classes['error'];

$default_back_url = $back_url ?? (APP_ROOT . '/data_sources');
@endphp

<div class="max-w-md mx-auto mt-8 p-6 {{ $container_class }} border rounded-lg">
    <div class="flex items-center">
        @if($type === 'error')
            <svg class="h-6 w-6 {{ $icon_class }} mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
        @elseif($type === 'warning')
            <svg class="h-6 w-6 {{ $icon_class }} mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
        @else
            <svg class="h-6 w-6 {{ $icon_class }} mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        @endif
        <h3 class="text-lg font-medium">{{ $title }}</h3>
    </div>
    <p class="mt-2 text-sm">{{ $message }}</p>
    
    @if($show_back_button)
        <div class="mt-4">
            <button onclick="window.history.back()" 
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white {{ $button_class }} focus:outline-none focus:ring-2 focus:ring-offset-2">
                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>
            <a href="{{ $default_back_url }}" 
               class="ml-3 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Data Sources
            </a>
        </div>
    @endif
</div>
