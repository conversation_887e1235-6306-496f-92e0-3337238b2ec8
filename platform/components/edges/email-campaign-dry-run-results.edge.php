{{-- Email Campaign Dry Run Results Modal --}}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
                Campaign Dry Run Results
            </h3>
            <p class="mt-1 text-sm text-gray-600">
                Simulation of campaign execution without sending actual emails
            </p>
        </div>

        <div class="p-6">
            {{-- Summary Statistics --}}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">{{ $results['total_processed'] }}</div>
                    <div class="text-sm text-blue-800">Recipients Processed</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">{{ $results['total_would_send'] }}</div>
                    <div class="text-sm text-green-800">Would Send Successfully</div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">{{ $results['total_would_skip'] }}</div>
                    <div class="text-sm text-yellow-800">Would Skip Total</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">{{ $results['total_would_fail'] }}</div>
                    <div class="text-sm text-red-800">Would Fail</div>
                </div>
            </div>

            {{-- Skip Breakdown --}}
            @if($results['total_would_skip'] > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
                    <div class="text-xl font-bold text-orange-600">{{ $results['total_would_skip_history'] ?? 0 }}</div>
                    <div class="text-sm text-orange-800">Already Sent (Previous Runs)</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div class="text-xl font-bold text-purple-600">{{ $results['total_would_skip_duplicates'] ?? 0 }}</div>
                    <div class="text-sm text-purple-800">Duplicate Emails (This Run)</div>
                </div>
            </div>
            @endif

            {{-- Errors Section --}}
            @if(!empty($results['errors']))
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">Errors Found</h4>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <ul class="list-disc list-inside space-y-1">
                        @foreach($results['errors'] as $error)
                        <li class="text-sm text-red-700">{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            </div>
            @endif

            {{-- Sample Emails Preview --}}
            @if(!empty($results['sample_emails']))
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">Sample Email Previews</h4>
                <div class="space-y-4">
                    @foreach($results['sample_emails'] as $index => $sample)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                            <div>
                                <span class="text-sm font-medium text-gray-700">To:</span>
                                <span class="text-sm text-gray-900">{{ $sample['to'] }}</span>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-700">From:</span>
                                <span class="text-sm text-gray-900">{{ $sample['from'] }}</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-700">Subject:</span>
                            <span class="text-sm text-gray-900">{{ $sample['subject'] }}</span>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-700">Body Preview:</span>
                            <div class="text-sm text-gray-600 bg-gray-50 p-2 rounded mt-1">
                                {{ $sample['body_preview'] }}
                            </div>
                        </div>
                        
                        {{-- Expandable full body --}}
                        <div class="mt-2">
                            <button type="button" 
                                    class="text-sm text-blue-600 hover:text-blue-800"
                                    onclick="toggleFullBody({{ $index }})">
                                <span id="toggle-text-{{ $index }}">Show Full Email</span>
                            </button>
                            <div id="full-body-{{ $index }}" class="hidden mt-2 text-sm text-gray-600 bg-gray-50 p-3 rounded max-h-64 overflow-y-auto">
                                {!! $sample['full_body'] !!}
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            {{-- Skipped Recipients Section --}}
            @if(!empty($results['skipped_recipients']))
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">
                    Skipped Recipients ({{ count($results['skipped_recipients']) }})
                </h4>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <p class="text-sm text-yellow-800 mb-3">
                        These recipients will be skipped because they have already been sent emails successfully for this campaign:
                    </p>
                    <div class="max-h-32 overflow-y-auto">
                        <ul class="list-disc list-inside space-y-1">
                            @foreach($results['skipped_recipients'] as $skipped)
                            <li class="text-sm text-yellow-700">
                                <strong>{{ $skipped['email'] }}</strong> ({{ $skipped['name'] }}) - {{ $skipped['reason'] }}
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            @endif

            {{-- Duplicate Recipients Section --}}
            @if(!empty($results['duplicate_recipients']))
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">
                    Duplicate Email Recipients ({{ count($results['duplicate_recipients']) }})
                </h4>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <p class="text-sm text-purple-800 mb-3">
                        These recipients will be skipped because their email addresses appear multiple times in the data source. Only the first occurrence will be sent:
                    </p>
                    <div class="max-h-32 overflow-y-auto">
                        <ul class="list-disc list-inside space-y-1">
                            @foreach($results['duplicate_recipients'] as $duplicate)
                            <li class="text-sm text-purple-700">
                                <strong>{{ $duplicate['email'] }}</strong> ({{ $duplicate['name'] }}) - {{ $duplicate['reason'] }}
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            @endif

            {{-- Duplicate Recipients Section --}}
            @if(!empty($results['duplicate_recipients']))
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">
                    Duplicate Email Recipients ({{ count($results['duplicate_recipients']) }})
                </h4>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <p class="text-sm text-purple-800 mb-3">
                        These recipients will be skipped because they have duplicate email addresses within this run (only the first occurrence will be sent):
                    </p>
                    <div class="max-h-32 overflow-y-auto">
                        <ul class="list-disc list-inside space-y-1">
                            @foreach($results['duplicate_recipients'] as $duplicate)
                            <li class="text-sm text-purple-700">
                                <strong>{{ $duplicate['email'] }}</strong> ({{ $duplicate['name'] }}) - {{ $duplicate['reason'] }}
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            @endif

            {{-- Recipients List --}}
            @if(!empty($results['recipients']))
            <div class="mb-6">
                <h4 class="text-md font-medium text-gray-900 mb-3">
                    All Recipients ({{ count($results['recipients']) }})
                </h4>
                <div class="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Subject</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Data Fields</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($results['recipients'] as $recipient)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-2 text-sm text-gray-900">{{ $recipient['email'] }}</td>
                                <td class="px-4 py-2 text-sm text-gray-900">{{ $recipient['name'] }}</td>
                                <td class="px-4 py-2 text-sm text-gray-600 max-w-xs truncate">{{ $recipient['subject'] }}</td>
                                <td class="px-4 py-2 text-sm text-gray-500">{{ count($recipient['data_fields']) }} fields</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif

            {{-- Action Buttons --}}
            <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    @if($results['total_would_send'] > 0)
                        Ready to send {{ $results['total_would_send'] }} emails
                    @else
                        No emails would be sent
                    @endif
                </div>
                <div class="flex space-x-3">
                    <button type="button"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                            onclick="document.querySelector('[x-data]').__x.$data.showModal = false">
                        Close
                    </button>
                    @if($results['total_would_send'] > 0)
                    <button type="button"
                            class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700"
                            hx-post="{{ APP_ROOT }}/api/email_campaigns/execute_campaign"
                            hx-vals='{"id": {{ $campaign_id }}}'
                            hx-target="#modal_body"
                            hx-confirm="Are you sure you want to execute this campaign? This will send {{ $results['total_would_send'] }} emails.">
                        Execute Campaign
                    </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFullBody(index) {
    const fullBody = document.getElementById('full-body-' + index);
    const toggleText = document.getElementById('toggle-text-' + index);
    
    if (fullBody.classList.contains('hidden')) {
        fullBody.classList.remove('hidden');
        toggleText.textContent = 'Hide Full Email';
    } else {
        fullBody.classList.add('hidden');
        toggleText.textContent = 'Show Full Email';
    }
}
</script>
