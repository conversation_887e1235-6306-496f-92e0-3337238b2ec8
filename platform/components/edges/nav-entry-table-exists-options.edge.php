@props([
    'table_error_message' => '',
    'new_entry_data' => [],
    'progress_id' => 'nav_entry_progress'
])

<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
    <div class="flex items-start">
        <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
            </svg>
        </div>
        <div class="ml-3 flex-1">
            <h3 class="text-sm font-medium text-yellow-800">
                Database Table Already Exists
            </h3>
            <div class="mt-2 text-sm text-yellow-700">
                <p>A database table for this navigation entry already exists and contains data.</p>
                
                <div class="mt-3 bg-white rounded border p-3">
                    <h4 class="font-medium text-gray-900 mb-2">Navigation Entry Details:</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li><strong>Name:</strong> {{ $new_entry_data['name'] }}</li>
                        <li><strong>Route Key:</strong> {{ $new_entry_data['key'] }}</li>
                        <li><strong>Parent Path:</strong> {{ $new_entry_data['parent_path'] }}</li>
                        <li><strong>Table Name:</strong> autobooks_{{ $new_entry_data['key'] }}_data</li>
                    </ul>
                </div>

                <div class="mt-3 bg-red-50 rounded border border-red-200 p-3">
                    <h4 class="font-medium text-red-800 mb-2">Error Details:</h4>
                    <p class="text-sm text-red-700">{{ $table_error_message }}</p>
                </div>

                <p class="mt-3">What would you like to do?</p>
            </div>
            
            <div class="mt-4 flex space-x-3">
                <button
                    type="button"
                    hx-post="{{ APP_ROOT }}/api/nav_tree/handle_duplicate_choice"
                    hx-vals='{"choice": "update"}'
                    hx-target="#{{ $progress_id }}_container"
                    hx-swap="innerHTML"
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                    <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Replace Existing Table & Data
                </button>
                
                <button
                    type="button"
                    hx-post="{{ APP_ROOT }}/api/nav_tree/handle_duplicate_choice"
                    hx-vals='{"choice": "cancel"}'
                    hx-target="#{{ $progress_id }}_container"
                    hx-swap="innerHTML"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                    <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                </button>
            </div>
            
            <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                <p class="text-xs text-blue-700">
                    <strong>Note:</strong> Choosing "Replace" will permanently delete all existing data in the table and create a new one. 
                    This action cannot be undone. If you want to keep existing data, choose "Cancel" and use the settings page to manage the data instead.
                </p>
            </div>
        </div>
    </div>
</div>
