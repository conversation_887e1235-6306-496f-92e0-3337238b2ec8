@props([
        'form_title' => 'Field Definition',
        'field_name' => 'new_field',
        'field' => [],
        'types' => ['text', 'number', 'date', 'boolean'],
        'categories' => ['customer', 'subscription', 'invoice', 'payment', 'custom'],
        'submit_action' => 'create',
        
])
@use system\unified_field_definitions

        <!-- Form -->
        <form id="field-definition-form"
              class="mt-6"
              hx-post="{{ APP_ROOT }}/api/unified_field_definitions/{{ $submit_action }}"
              hx-target="#modal_body"
              hx-swap="innerHTML"
              hx-encoding="multipart/form-data">

            <!-- Information Banner -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <h4 class="text-sm font-medium text-blue-900 mb-2">Field Definition Controls Three Separate Functions</h4>
                        <div class="text-xs text-blue-800 space-y-1">
                            <div class="flex items-center">
                                <span class="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                                <strong>CSV Column Recognition:</strong> Always enabled - uses "Field Patterns" to recognize column names during data import
                            </div>
                            <div class="flex items-center">
                                <span class="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                                <strong>Customer Matching Rules:</strong> Optional - when enabled, field appears in rules container for automated customer matching
                            </div>
                            <div class="flex items-center">
                                <span class="inline-block w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                                <strong>Data Table Display:</strong> Configurable - controls default visibility and priority in data tables and UI
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Left Column: Basic Information -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900 border-b pb-2">Basic Information</h4>
                    
                    <!-- Field Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Field Name</label>
                        <input type="text" 
                               name="field_name" 
                               value="{{ htmlspecialchars($is_new ? '' : $field_name) }}"
                               {{ $is_new ? '' : 'readonly' }}
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 {{ $is_new ? '' : 'bg-gray-50' }}"
                               required>
                        @if(!$is_new)
                            <p class="text-xs text-gray-500 mt-1">Field name cannot be changed after creation</p>
                        @endif
                    </div>

                    <!-- Label -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Label</label>
                        <input type="text" 
                               name="label" 
                               value="{{ htmlspecialchars($field['label'] ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                               required>
                    </div>

                    <!-- Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <select name="type" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                required>
                            @foreach($types as $type)
                                <option value="{{ $type }}" {{ ($field['type'] ?? '') === $type ? 'selected' : '' }}>
                                    {{ ucfirst($type) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Category -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select name="category" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                required>
                            @foreach($categories as $category)
                                <option value="{{ $category }}" {{ ($field['category'] ?? '') === $category ? 'selected' : '' }}>
                                    {{ ucwords(str_replace('_', ' ', $category)) }}
                                </option>
                            @endforeach
                            <option value="custom" {{ ($field['category'] ?? '') === 'custom' ? 'selected' : '' }}>Custom</option>
                        </select>
                    </div>

                    <!-- Description -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea name="description" 
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  required>{{ htmlspecialchars($field['description'] ?? '') }}</textarea>
                    </div>
                </div>

                <!-- Right Column: Advanced Configuration -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between border-b pb-2 mb-4">
                        <h4 class="text-md font-medium text-gray-900">CSV Column Recognition</h4>
                        <div class="flex items-center text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Always Active
                        </div>
                    </div>

                    <!-- Field Patterns -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Field Patterns</label>
                        <textarea name="patterns"
                                  rows="4"
                                  placeholder="Enter field patterns, one per line"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  required>{{ implode("\n", $field['patterns'] ?? []) }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">
                            CSV column name variations that should be recognized as this field during data import.
                            <strong>This is always active</strong> regardless of matching or display settings.
                        </p>
                    </div>

                    <!-- Normalized Fields -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Normalized Fields</label>
                        <textarea name="normalized_fields" 
                                  rows="3"
                                  placeholder="Enter normalized field names, one per line"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                  required>{{ implode("\n", $field['normalized_fields'] ?? []) }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">Standard field names this field should map to</p>
                    </div>

                    <!-- Matching Configuration -->
                    <div class="border border-gray-200 rounded-md p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Customer Matching Rules</h5>
                                <p class="text-xs text-gray-600 mt-1">Controls if this field appears in the subscription matching rules container below</p>
                            </div>
                            <div class="flex items-center text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Rules Container
                            </div>
                        </div>

                        <div class="space-y-3">
                            <!-- Enabled for Matching -->
                            <div class="flex items-start">
                                <input type="checkbox"
                                       name="matching_enabled"
                                       id="matching_enabled"
                                       {{ ($field['matching']['enabled'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mt-0.5">
                                <div class="ml-2">
                                    <label for="matching_enabled" class="text-sm text-gray-700 font-medium">
                                        Enable for customer matching
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">
                                        When enabled, this field will appear in the subscription matching rules container and be used to automatically match CSV data to existing customers.
                                        <strong>Note:</strong> Disabling this does NOT affect CSV column recognition or data import.
                                    </p>
                                </div>
                            </div>

                            <!-- Priority -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                                <input type="number" 
                                       name="matching_priority" 
                                       value="{{ $field['matching']['priority'] ?? 10 }}"
                                       min="0"
                                       max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <p class="text-xs text-gray-500 mt-1">Lower numbers = higher priority</p>
                            </div>

                            <!-- Fuzzy Matching -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       name="fuzzy_matching" 
                                       id="fuzzy_matching"
                                       {{ ($field['matching']['fuzzy_matching'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="fuzzy_matching" class="ml-2 text-sm text-gray-700">
                                    Enable fuzzy matching
                                </label>
                            </div>

                            <!-- Similarity Threshold -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Similarity Threshold (%)</label>
                                <input type="number" 
                                       name="similarity_threshold" 
                                       value="{{ $field['matching']['similarity_threshold'] ?? 70 }}"
                                       min="0"
                                       max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>

                            <!-- Case Sensitive -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       name="case_sensitive" 
                                       id="case_sensitive"
                                       {{ ($field['matching']['case_sensitive'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="case_sensitive" class="ml-2 text-sm text-gray-700">
                                    Case sensitive matching
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Display Configuration -->
                    <div class="border border-gray-200 rounded-md p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <h5 class="text-sm font-medium text-gray-900">Data Table Display Settings</h5>
                                <p class="text-xs text-gray-600 mt-1">Controls how this field appears in data tables and column selectors</p>
                            </div>
                            <div class="flex items-center text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"></path>
                                </svg>
                                UI Display
                            </div>
                        </div>

                        <div class="space-y-3">
                            <!-- Show by Default -->
                            <div class="flex items-start">
                                <input type="checkbox"
                                       name="show_by_default"
                                       id="show_by_default"
                                       {{ ($field['display']['show_by_default'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mt-0.5">
                                <div class="ml-2">
                                    <label for="show_by_default" class="text-sm text-gray-700 font-medium">
                                        Show by default in data tables
                                    </label>
                                    <p class="text-xs text-gray-500 mt-1">
                                        When enabled, this field will be visible by default in data tables. Users can still hide/show it using column controls.
                                        This setting is separate from CSV import and customer matching.
                                    </p>
                                </div>
                            </div>

                            <!-- Category Priority -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Category Priority</label>
                                <input type="number"
                                       name="category_priority"
                                       value="{{ $field['display']['category_priority'] ?? 5 }}"
                                       min="1"
                                       max="12"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <p class="text-xs text-gray-500 mt-1">Lower numbers = higher priority (1 = highest, 12 = lowest)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200 mt-6">
                <div>
                    @if(!$is_new && !unified_field_definitions::is_custom_field($field_name))
                        <button type="button"
                                class="px-4 py-2 text-sm font-medium text-orange-700 bg-orange-100 border border-orange-300 rounded-md hover:bg-orange-200"
                                hx-post="{{ APP_ROOT }}/api/unified_field_definitions/reset"
                                hx-vals='{"field_name": "{{ $field_name }}"}'
                                hx-target="#modal_body"
                                hx-swap="outerHTML"
                                hx-confirm="Are you sure you want to reset this field to its default configuration? This will remove any custom changes.">
                            Reset to Default
                        </button>
                    @endif
                </div>
                
                <div class="flex space-x-3">
                    <button type="button"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                            @click="modal = false">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700">
                        {{ $is_new ? 'Create Field' : 'Save Changes' }}
                    </button>
                </div>
            </div>
        </form>

<!-- Form processing handled by HTMX, modal interactions handled by Alpine.js -->
