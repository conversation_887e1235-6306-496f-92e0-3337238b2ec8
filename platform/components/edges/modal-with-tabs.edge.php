@props([
    'tabs' => [],
    'currentTab' => null,
    'modalOpen' => false
])

@if($modalOpen && !empty($tabs))
<div id="modal_container">
    <!-- Tab Bar -->
    <div id="modal_tab_bar" class="border-b border-gray-200 mb-4">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            @foreach($tabs as $tab)
            <div class="flex items-center group">
                <button
                    hx-post="{{ APP_ROOT }}/api/modal_tabs/switch_tab"
                    hx-vals='{"tab_id": {{ $tab['id'] }}}'
                    hx-target="#modal_body"
                    class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm 
                           {{ $tab['id'] == $currentTab ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                    {{ $tab['title'] }}
                </button>
                
                <!-- Pin Button -->
                <button
                    hx-post="{{ APP_ROOT }}/api/modal_tabs/toggle_pin"
                    hx-vals='{"tab_id": {{ $tab['id'] }}}'
                    class="ml-2 p-1 rounded hover:bg-gray-100 {{ $tab['pinned'] ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-600' }}"
                    title="{{ $tab['pinned'] ? 'Unpin tab' : 'Pin tab' }}">
                    @if($tab['pinned'])
                        <!-- Pinned icon -->
                        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8zM9 5a2 2 0 00-2 2v6H6a1 1 0 100 2h8a1 1 0 100-2h-1V7a2 2 0 00-2-2H9zM7 8a1 1 0 012-2h2a1 1 0 110 2H9a1 1 0 01-2 0z" clip-rule="evenodd"/>
                        </svg>
                    @else
                        <!-- Unpinned icon -->
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    @endif
                </button>
                
                <!-- Close Button -->
                <button
                    hx-post="{{ APP_ROOT }}/api/modal_tabs/close_tab"
                    hx-vals='{"tab_id": {{ $tab['id'] }}}'
                    class="ml-2 p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                    title="Close tab">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            @endforeach
        </nav>
    </div>
    
    <!-- Tab Content -->
    <div id="modal_body">
        @foreach($tabs as $tab)
            @if($tab['id'] == $currentTab)
                {!! $tab['content'] !!}
            @endif
        @endforeach
    </div>
</div>
@endif
