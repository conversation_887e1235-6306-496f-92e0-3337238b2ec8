@props([
    'title' => 'JSON Viewer',
    'description' => 'A component for viewing JSON data',
    'json_data' => '{}',
    'id' => 'json_viewer_' . rand(0,10000),
    'class_suffix' => '',
    'height' => '400px',
    'expandAll' => false
])

@php
    // Ensure we have valid JSON
    $json_object = json_decode($json_data);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $json_data = '{}';
    } else {
        // Re-encode to ensure proper formatting
        $json_data = json_encode($json_object, JSON_PRETTY_PRINT);
    }
    
    // Prepare viewer options
    $viewer_options = [
        'mode' => 'view',
        'modes' => ['view'],
        'search' => true,
        'navigationBar' => true,
        'statusBar' => true,
        'mainMenuBar' => false,
        'expandAll' => $expandAll
    ];
    
    // Convert options to JSON for JavaScript
    $viewer_options_json = json_encode($viewer_options);
@endphp

<div class="w-full {{ $class_suffix }}">
    @if (!empty($title))
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-2">{{ $title }}</h3>
    @endif
    
    <div id="{{ $id }}_container" class="border border-gray-300 rounded-md" style="height: {{ $height }}">
        <div id="{{ $id }}" class="w-full h-full"></div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create the viewer
            const container = document.getElementById('{{ $id }}');
            const options = {!! $viewer_options_json !!};
            
            // Initialize the viewer with the JSON data
            const viewer = new JSONEditor(container, options);
            const initialJson = {!! $json_data !!};
            viewer.set(initialJson);
            
            // Expand all nodes if specified
            if (options.expandAll) {
                viewer.expandAll();
            }
        });
    </script>
</div>
