@props([
    'title' => 'pagination strip',
    'description' => 'displays pagination stats and buttons',
    'first_item' => 0, // item to start frm
    'item_count' => 1, // data array of items to page
    'items_per_page' => 30, // breakpoint to pages
    'current_page_num' => 1
])


<div class="flex bottom-0 items-center justify-between border-t border-gray-200 bg-white px-4 py-2 sm:px-6">
    <div class="flex flex-1 justify-between sm:hidden">
        <a href="#"
           hx-get="{{ APP_ROOT }}/api/data_table/pagination"
           hx-target=".search_target"
           hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
           hx-vals='{"page": {{ max(1, $pagination->current_page_number - 1) }}}'
           class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
        <a href="#"
           hx-get="{{ APP_ROOT }}/api/data_table/pagination"
           hx-target=".search_target"
           hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
           hx-vals='{"page": {{ min($pagination->total_pages, $pagination->current_page_number + 1) }}}'
           class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
    </div>
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                Showing
                <span class="font-medium">{{ $pagination->first_item }}</span>
                to
                <span class="font-medium">{{ $pagination->last_item }}</span>
                of
                <span class="font-medium">{{ $pagination->item_count }} </span>
                results
            </p>
        </div>
        <div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <a href="#"
                   hx-get="{{ APP_ROOT }}/api/data_table/pagination"
                   hx-target=".search_target"
                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                   hx-vals='{"page": {{ max(1, $pagination->current_page_number - 1) }}}'
                   class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                    <span class="sr-only">Previous</span>
                    <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                        <path fill-rule="evenodd"
                              d="M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z"
                              clip-rule="evenodd"/>
                    </svg>
                </a>
                <!-- Current: "z-10 bg-indigo-600 text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600", Default: "text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0" -->
                @foreach($pagination->page_array() as $page)
                    @if($page === '...')
                        <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">...</span>
                    @else
                        <a href="#"
                           hx-get="{{ APP_ROOT }}/api/data_table/pagination"
                           hx-target=".search_target"
                           hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                           hx-vals='{"page": {{ $page }}}'
                           {{ $pagination->current_page_number == $page ? 'aria-current="page" class="relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"' : 'class="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex"' }}>
                           {{ $page }}
                        </a>
                    @endif
                @endforeach

                <!-- <a href="#" aria-current="page" class="relative z-10 inline-flex items-center bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">{{ $pagination_page }}</a>
                                                <a href="#" class="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex">{{ $pagination_page + 2 }}</a>
                                                <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0">...</span>
                                                <a href="#" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex">  @if(is_countable($items)) {{ ceil(count($items) / $pagination) - 2 }} @endif</a>
                                                <a href="#" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"> @if(is_countable($items)) {{ ceil(count($items) / $pagination) - 1 }} @endif</a>
                                                <a href="#" class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"> @if(is_countable($items)) {{ ceil(count($items) / $pagination) }} @endif</a>
                                                <a href="#" class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                                  -->
                <a href="#"
                   hx-get="{{ APP_ROOT }}/api/data_table/pagination"
                   hx-target=".search_target"
                   hx-include="[name='search_terms'], [name='per_page'], .data_table_filter"
                   hx-vals='{"page": {{ min($pagination->total_pages, $pagination->current_page_number + 1) }}}'
                   class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                <span class="sr-only">Next</span>
                <svg class="size-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                    <path fill-rule="evenodd"
                          d="M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z"
                          clip-rule="evenodd"/>
                </svg>
                </a>
            </nav>
        </div>
    </div>
</div>