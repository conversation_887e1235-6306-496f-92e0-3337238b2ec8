@props([
    'title' => 'Users',
    'description' => 'A list of all the users in your account including their name, title, email and role.',
    'items' => [], // data array of items
    'id_count' => edge::id_count(),
    'columns' => [[
            'label' => 'Name',
            'fields' => ['name'],
            'extra_parameters' => ''
     ]], // An array of column definitions: ['label' => 'Name', 'fields' => ['name'], 'filter' => false]
    'rows' => [
       'id_prefix' => 'row_',
       'id_field' => 'id',
       'class_postfix' => '',
       'extra_parameters' => ''
    ],// An array of row parameters such as ids,classes  etc
    'just_body' => false, // just return body
    'just_rows' => false, // just return rows
    'current_page_num' => 1,
    'class' => '', //extra classes=
    'callback' => null,
])

<div class="px-4 sm:px-6 lg:px-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-base font-semibold text-gray-900">{{ $title }}</h1>
            <p class="mt-2 text-sm text-gray-700">{{ $description }}</p>
        </div>
    </div>
    <form method="POST" action="{{ $callback ?? '#' }}">
        <div class="mt-8 flow-root">
            <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <table class="min-w-full divide-y divide-gray-300 {{ $class }}">
                        <thead>
                        <tr>
                            @foreach($columns as $column)
                                <th scope="col" class="py-3.5 pr-3 pl-4 text-left text-sm font-semibold whitespace-nowrap text-gray-900 sm:pl-0">{{ $column['label'] }}</th>
                            @endforeach
                            <th scope="col" class="relative py-3.5 pr-4 pl-3 whitespace-nowrap sm:pr-0">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 bg-white">
                        @foreach($items as $index => $item)
                            <tr id="{{ $rows['id_prefix'] . $item[$rows['id_field']] }}" class="{{ $rows['class_postfix'] }}" {{ $rows['extra_parameters'] }}>
                                @foreach($columns as $column)
                                    <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-500">
                                        @php
                                            $first_field = $column['fields'][0] ?? '';
                                        @endphp
                                        <input
                                            type="text"
                                            name="items[{{ $index }}][{{ $first_field }}]"
                                            value="{{ $item[$first_field] ?? '' }}"
                                            class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                            {{ $column['extra_parameters'] }}
                                        >
                                        <input type="hidden" name="items[{{ $index }}][{{ $rows['id_field'] }}]" value="{{ $item[$rows['id_field']] }}">
                                    </td>
                                @endforeach
                                <td class="relative py-2 pr-4 pl-3 text-right text-sm font-medium whitespace-nowrap sm:pr-0">
                                    <button type="submit" class="text-indigo-600 hover:text-indigo-900">Save</button>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </form>
</div>