@props([
    'table_name' => '',
    'callback' => '',
    'column_id' => '',
    'loop_index' => 0,
    'available_field_list' => []
])

<div class="mb-2 p-2 bg-green-50 border border-green-200 rounded">
    <form hx-post="{{ APP_ROOT }}/api/data_table/column_preferences/add_action_button"
          hx-vals='{"table_name": "{{ $table_name }}", "callback": "{{ $callback }}", "column_id": "{{ $column_id }}"}'
          hx-target=".data_table"
          hx-swap="outerHTML"
          hx-on::after-request="document.querySelector('[data-column-id=\"{{ $column_id }}\"] .add-action-form-container').innerHTML = ''"
          class="space-y-3">

        <!-- Template Dropdown -->
        <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Template:</label>
            <select name="template" required
                    class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                <option value="">Select template...</option>
                @php
                    // Cache template scanning to avoid repeated filesystem operations
                    static $cached_action_templates = null;
                    if ($cached_action_templates === null) {
                        $templates = [];
                        $template_dirs = [
                            FS_SYS_COMPONENTS . DS . 'edges',
                            FS_RESOURCES . DS . 'components' . DS . 'edges'
                        ];

                        foreach ($template_dirs as $template_dir) {
                            if (is_dir($template_dir)) {
                                $files = scandir($template_dir);
                                foreach ($files as $file) {
                                    if (pathinfo($file, PATHINFO_EXTENSION) === 'php' && strpos($file, '.edge.') !== false) {
                                        $template_name = str_replace('.edge.php', '', $file);
                                        if (strpos($template_name, 'action-') === 0) { // Only show action templates
                                            $templates[] = $template_name;
                                        }
                                    }
                                }
                            }
                        }

                        // Remove duplicates and sort
                        $templates = array_unique($templates);
                        sort($templates);
                        $cached_action_templates = $templates;
                    } else {
                        $templates = $cached_action_templates;
                    }
                @endphp
                @foreach($templates as $template)
                    <option value="{{ $template }}">{{ ucwords(str_replace(['-', '_'], ' ', $template)) }}</option>
                @endforeach
            </select>
        </div>

        <!-- Field Dropdown -->
        <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Field to pass:</label>
            <select name="field" required
                    class="w-full text-xs border border-gray-300 rounded px-2 py-1">
                <option value="">Select field...</option>
                @foreach($available_field_list as $field)
                    <option value="{{ $field }}">{{ $field }}</option>
                @endforeach
            </select>
        </div>

        <!-- Icon Dropdown -->
        <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Icon:</label>
            <x-forms-select-with-icons name="icon" :options="array_keys(ICONS)"></x-forms-select-with-icons>
        </div>

        <!-- Form Buttons -->
        <div class="flex gap-2">
            <button type="submit"
                    class="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700">
                Add Action
            </button>
            <button type="button"
                    hx-get="{{ APP_ROOT }}/api/data_table/column_preferences/cancel_add_action"
                    hx-target="closest .add-action-form-container"
                    hx-swap="innerHTML"
                    class="text-xs px-2 py-1 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">
                Cancel
            </button>
        </div>
    </form>
</div>
