@props([
    'title' => 'Template Selector',
    'description' => 'Select a template from the templates directory',
    'name' => 'template',
    'label' => 'Select a template',
    'selected' => null, // Pre-selected template
    'class' => '',
    'extra_attributes' => '',
])

@php
// Get all template files from the templates directory
$template_dir = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/components/templates/';
$template_files = glob($template_dir . '*.edge.php');

// Format template options for the select
$options = [];
foreach ($template_files as $file) {
    $filename = basename($file, '.edge.php');
    $display_name = ucwords(str_replace('-', ' ', $filename));
    
    // Read the first few lines to extract title and description
    $content = file_get_contents($file);
    $title = $display_name;
    $description = '';
    
    if (preg_match("/'title' => '([^']+)'/", $content, $matches)) {
        $title = $matches[1];
    }
    
    if (preg_match("/'description' => '([^']+)'/", $content, $matches)) {
        $description = $matches[1];
    }
    
    $options[] = [
        'id' => $filename,
        'name' => $title,
        'description' => $description,
        'icon' => 'document-text'
    ];
}
@endphp

<x-forms-select-with-icons
    name="{{ $name }}"
    label="{{ $label }}"
    :options="$options"
    :selected="$selected"
    class="{{ $class }}"
    {{ $extra_attributes }}
/>
