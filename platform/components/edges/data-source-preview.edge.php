@props([
    'title' => 'Data Source Preview',
    'description' => 'Preview data from configured data source',
    'data_source' => [],
    'sample_data' => [],
    'table_info' => []
])

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <button hx-get="{{ APP_ROOT }}/data_sources"
                                    hx-target="#content_wrapper"
                                    class="text-gray-400 hover:text-gray-500">
                                Data Sources
                            </button>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Preview</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="mt-2 text-3xl font-bold text-gray-900">{{ $data_source['name'] ?? 'Data Source' }}</h1>
                <p class="mt-1 text-sm text-gray-600">{{ $data_source['description'] ?: $description }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <button hx-get="{{ APP_ROOT }}/api/data_sources/edit_view" 
                        hx-vals='{"id": {{ $data_source['id'] ?? 0 }}}'
                        hx-target="#content_wrapper"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                </button>
            </div>
        </div>
    </div>
    
    <!-- Data Source Info -->
    <div class="bg-white shadow rounded-lg p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Data Source Information</h2>
        
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div>
                <dt class="text-sm font-medium text-gray-500">Table Name</dt>
                <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $data_source['table_name'] ?? 'N/A' }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Category</dt>
                <dd class="mt-1">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {{ ucfirst($data_source['category'] ?? 'other') }}
                    </span>
                </dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Status</dt>
                <dd class="mt-1">
                    @if(($data_source['status'] ?? 'active') === 'active')
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Active
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Inactive
                        </span>
                    @endif
                </dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Total Rows</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ number_format($table_info['row_count'] ?? 0) }}</dd>
            </div>
        </div>
        
        @if(!empty($data_source['filters']))
            <div class="mt-6">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Applied Filters</h3>
                <div class="space-y-2">
                    @foreach($data_source['filters'] as $filter)
                        <div class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                            <span class="font-mono">{{ $filter['column'] }}</span>
                            <span class="mx-1">{{ $filter['operator'] }}</span>
                            @if(!in_array($filter['operator'], ['IS NULL', 'IS NOT NULL']))
                                <span class="font-mono">{{ $filter['value'] }}</span>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
    
    <!-- Data Preview -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900">Data Preview</h2>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    @if($sample_data['success'] ?? false)
                        <span>Showing {{ number_format($sample_data['count'] ?? 0) }} of {{ number_format($table_info['row_count'] ?? 0) }} records</span>
                    @endif
                    <button onclick="window.location.reload()" 
                            class="text-indigo-600 hover:text-indigo-500 font-medium">
                        Refresh
                    </button>
                </div>
            </div>
        </div>
        
        @if(($sample_data['success'] ?? false) && !empty($sample_data['data']))
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            @foreach(array_keys($sample_data['data'][0]) as $column)
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {{ $column }}
                                </th>
                            @endforeach
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($sample_data['data'] as $row)
                            <tr class="hover:bg-gray-50">
                                @foreach($row as $value)
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @if(is_null($value))
                                            <span class="text-gray-400 italic">null</span>
                                        @elseif(is_string($value) && strlen($value) > 100)
                                            <span title="{{ $value }}">{{ substr($value, 0, 100) }}...</span>
                                        @elseif(is_array($value) || is_object($value))
                                            <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ json_encode($value) }}</code>
                                        @else
                                            {{ $value }}
                                        @endif
                                    </td>
                                @endforeach
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @elseif(($sample_data['success'] ?? false) && empty($sample_data['data']))
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No data available</h3>
                <p class="mt-1 text-sm text-gray-500">This data source contains no records or all records are filtered out.</p>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading data</h3>
                <p class="mt-1 text-sm text-gray-500">{{ $sample_data['error'] ?? 'Unknown error occurred' }}</p>
                <div class="mt-6">
                    <button onclick="window.location.reload()" 
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Try Again
                    </button>
                </div>
            </div>
        @endif
    </div>
    
    <!-- Column Information -->
    @if($table_info && !empty($table_info['columns']))
        <div class="mt-8 bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Column Information</h2>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Null</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Key</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Default</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($table_info['columns'] as $column)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ $column['Field'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                                    {{ $column['Type'] }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $column['Null'] === 'YES' ? 'Yes' : 'No' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @if($column['Key'] === 'PRI')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Primary
                                        </span>
                                    @elseif($column['Key'] === 'UNI')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Unique
                                        </span>
                                    @elseif($column['Key'] === 'MUL')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Index
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                                    {{ $column['Default'] ?? 'NULL' }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
</div>
