{{-- 
    Universal Sortable Example Template
    
    This template demonstrates how to use the new simplified sortable system.
    The sortable system works with any element that has the 'sortable' class
    and uses HTMX inline attributes for server communication.
--}}

{{-- Basic Sortable List Example --}}
<div class="mb-8">
    <h3 class="text-lg font-semibold mb-4">Basic Sortable List</h3>
    
    <div class="sortable bg-white border rounded-lg p-4"
         hx-post="{{ APP_ROOT }}/api/sortable/reorder_items"
         hx-trigger="sorted"
         hx-target="#result-basic"
         hx-swap="innerHTML">
        
        <div class="sortable-item p-3 mb-2 bg-gray-50 border rounded cursor-move" data-item-id="1">
            <input type="hidden" name="item_ids[]" value="1">
            Item 1 - Drag me around
        </div>
        
        <div class="sortable-item p-3 mb-2 bg-gray-50 border rounded cursor-move" data-item-id="2">
            <input type="hidden" name="item_ids[]" value="2">
            Item 2 - I can be sorted too
        </div>
        
        <div class="sortable-item p-3 mb-2 bg-gray-50 border rounded cursor-move" data-item-id="3">
            <input type="hidden" name="item_ids[]" value="3">
            Item 3 - Sort me please
        </div>
        
        <div class="sortable-item p-3 mb-2 bg-gray-50 border rounded cursor-move" data-item-id="4">
            <input type="hidden" name="item_ids[]" value="4">
            Item 4 - Last but not least
        </div>
    </div>
    
    <div id="result-basic" class="mt-4 p-3 bg-green-50 border border-green-200 rounded">
        Ready to sort! Drag items to reorder them.
    </div>
</div>

{{-- Sortable with Handle Example --}}
<div class="mb-8">
    <h3 class="text-lg font-semibold mb-4">Sortable with Drag Handle</h3>
    
    <div class="sortable bg-white border rounded-lg p-4"
         data-sortable-handle=".drag-handle"
         hx-post="{{ APP_ROOT }}/api/sortable/reorder_tasks"
         hx-trigger="sorted"
         hx-target="#result-handle"
         hx-swap="innerHTML">
        
        <div class="sortable-item flex items-center p-3 mb-2 bg-gray-50 border rounded" data-task-id="task1">
            <input type="hidden" name="task_ids[]" value="task1">
            <div class="drag-handle mr-3 p-2 text-gray-400 hover:text-gray-600 cursor-grab">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                </svg>
            </div>
            <div class="flex-1">
                <h4 class="font-medium">Task 1</h4>
                <p class="text-sm text-gray-600">This task can only be dragged by the handle</p>
            </div>
        </div>
        
        <div class="sortable-item flex items-center p-3 mb-2 bg-gray-50 border rounded" data-task-id="task2">
            <input type="hidden" name="task_ids[]" value="task2">
            <div class="drag-handle mr-3 p-2 text-gray-400 hover:text-gray-600 cursor-grab">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                </svg>
            </div>
            <div class="flex-1">
                <h4 class="font-medium">Task 2</h4>
                <p class="text-sm text-gray-600">Another task with drag handle</p>
            </div>
        </div>
        
        <div class="sortable-item flex items-center p-3 mb-2 bg-gray-50 border rounded" data-task-id="task3">
            <input type="hidden" name="task_ids[]" value="task3">
            <div class="drag-handle mr-3 p-2 text-gray-400 hover:text-gray-600 cursor-grab">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                </svg>
            </div>
            <div class="flex-1">
                <h4 class="font-medium">Task 3</h4>
                <p class="text-sm text-gray-600">Third task in the list</p>
            </div>
        </div>
    </div>
    
    <div id="result-handle" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
        Use the drag handles (⋮⋮⋮) to reorder tasks.
    </div>
</div>

{{-- Sortable Groups Example --}}
<div class="mb-8">
    <h3 class="text-lg font-semibold mb-4">Sortable Groups (Items can move between lists)</h3>
    
    <div class="grid grid-cols-2 gap-4">
        <div>
            <h4 class="font-medium mb-2">To Do</h4>
            <div class="sortable min-h-32 bg-red-50 border-2 border-red-200 rounded-lg p-4"
                 data-sortable-group="tasks"
                 data-list-id="todo"
                 hx-post="{{ APP_ROOT }}/api/sortable/move_between_lists"
                 hx-trigger="sorted"
                 hx-target="#result-groups"
                 hx-swap="innerHTML">
                
                <div class="sortable-item p-2 mb-2 bg-white border rounded cursor-move" data-task-id="todo1">
                    <input type="hidden" name="task_ids[]" value="todo1">
                    <input type="hidden" name="list_id" value="todo">
                    Write documentation
                </div>
                
                <div class="sortable-item p-2 mb-2 bg-white border rounded cursor-move" data-task-id="todo2">
                    <input type="hidden" name="task_ids[]" value="todo2">
                    <input type="hidden" name="list_id" value="todo">
                    Fix bugs
                </div>
            </div>
        </div>
        
        <div>
            <h4 class="font-medium mb-2">Done</h4>
            <div class="sortable min-h-32 bg-green-50 border-2 border-green-200 rounded-lg p-4"
                 data-sortable-group="tasks"
                 data-list-id="done"
                 hx-post="{{ APP_ROOT }}/api/sortable/move_between_lists"
                 hx-trigger="sorted"
                 hx-target="#result-groups"
                 hx-swap="innerHTML">
                
                <div class="sortable-item p-2 mb-2 bg-white border rounded cursor-move" data-task-id="done1">
                    <input type="hidden" name="task_ids[]" value="done1">
                    <input type="hidden" name="list_id" value="done">
                    Create sortable system
                </div>
            </div>
        </div>
    </div>
    
    <div id="result-groups" class="mt-4 p-3 bg-purple-50 border border-purple-200 rounded">
        Drag tasks between the To Do and Done lists.
    </div>
</div>

{{-- Configuration Options --}}
<div class="mb-8">
    <h3 class="text-lg font-semibold mb-4">Available Configuration Options</h3>
    
    <div class="bg-gray-50 border rounded-lg p-4">
        <h4 class="font-medium mb-2">Data Attributes for Sortable Container:</h4>
        <ul class="list-disc list-inside space-y-1 text-sm">
            <li><code>data-sortable-group="group-name"</code> - Allow items to move between containers with same group</li>
            <li><code>data-sortable-handle=".handle-class"</code> - Specify drag handle selector</li>
            <li><code>data-sortable-swap-threshold="0.65"</code> - Set swap threshold (0-1)</li>
            <li><code>data-sortable-fallback-on-body="true"</code> - Enable fallback on body</li>
        </ul>
        
        <h4 class="font-medium mb-2 mt-4">HTMX Attributes for Server Communication:</h4>
        <ul class="list-disc list-inside space-y-1 text-sm">
            <li><code>hx-post="/your/endpoint"</code> - Server endpoint to handle reordering</li>
            <li><code>hx-trigger="sorted"</code> - Trigger on sort completion</li>
            <li><code>hx-target="#result"</code> - Where to display response</li>
            <li><code>hx-swap="innerHTML"</code> - How to swap response</li>
        </ul>
        
        <h4 class="font-medium mb-2 mt-4">Required Structure:</h4>
        <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Container must have <code>sortable</code> class</li>
            <li>Items should have <code>data-item-id</code> or similar identifier</li>
            <li>Hidden inputs with item IDs for form submission</li>
            <li>HTMX will automatically collect form data and send to server</li>
        </ul>
    </div>
</div>
