@props([
    'title' => 'Subscription Display',
    'description' => '',
    'items' => [], 
    'data' => [],
    'histdata' => [],
    'class' => ''
])
<div class="grid grid-cols-3 bg-gray-200">
@foreach ($data as $card)

    <div class="flex flex-col {{ $card['class_suffix'] }}">

    <x-{{ $card['type'] ?? 'layout-card' }}
        :label="$card['label']"
        collapsible="true" 
        collapsed="{{ $card['collapsed'] ?? 'false' }}"
        class_suffix=""
        internal_class_suffix="{{ $card['internal_class_suffix'] }}"
    >

        @foreach ($card['content'] as $key => $column)
            @if ($column['type'] == 'component-activity-feed')
                <x-component-activity-feed :activity="$histdata" />
            @else
                @if (!isset($column['content']))
                    <div class="px-1 py-1 h-min sm:grid sm:grid-cols-2 sm:gap-4 sm:px-0">
                        <dt class="text-sm/6 font-medium text-gray-900">{{ $column['label'] }}</dt>
                        <dd class="mt-1 text-sm/6 text-gray-700 sm:mt-0">
                            {{ $column['value'] }}
                        </dd>
                    </div>
                @else
                    <x-{{ $column['type'] ?? 'layout-card' }}
                        :label="$column['label']"
                        collapsible="true"
                        :collapsed="{{ $column['collapsed'] ?? 'false' }}"
                        :class_suffix="{{ $column['class_suffix'] }}"
                        :internal_class_suffix="{{ $column['internal_class_suffix'] }}"
                    >
                        @foreach ($column['content'] as $subKey => $subColumn)
                            <div class="{{ isset($column['hide_labels']) && $column['hide_labels'] ? 'px-0 py-0' : 'px-1 py-1' }}  h-min sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                                <dt class="text-sm/6 font-medium text-gray-900 {{ isset($column['hide_labels']) && $column['hide_labels'] ? 'hidden' : '' }}">
                                    {{ $subColumn['label'] }}
                                </dt>
                                <dd class=" text-sm/6 text-gray-700 {{ isset($column['hide_labels']) && $column['hide_labels'] ? 'mt-0 col-span-3' : 'mt-1 sm:col-span-2' }} sm:mt-0">
                                    {{ $subColumn['value'] }}
                                </dd>
                            </div>
                        @endforeach
                    </x-{{ $column['type'] ?? 'layout-card' }}>
                @endif
            @endif
        @endforeach

    </x-{{ $card['type'] ?? 'layout-card' }}>
    </div>
@endforeach
</div>