[database_errors] [2025-08-20 09:43:52] [database.class.php:886] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":73,"created_at":"2025-08-20 09:43:52"}"\n    [":data_source_id"]: int(73)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(686): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(426): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(138): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 886\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:43:52\"}",":data_source_id":73},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(686): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(426): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(138): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 690\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:43:52\"}",":data_source_id":73}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 426\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:43:52\"}",":data_source_id":73}\n-->\n
[database_errors] [2025-08-20 09:46:08] [database.class.php:886] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":73,"created_at":"2025-08-20 09:46:08"}"\n    [":data_source_id"]: int(73)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(686): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(426): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(138): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 886\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:46:08\"}",":data_source_id":73},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(686): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(426): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(138): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 690\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:46:08\"}",":data_source_id":73}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 426\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:46:08\"}",":data_source_id":73}\n-->\n
[database_errors] [2025-08-20 09:50:01] [database.class.php:886] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":73,"created_at":"2025-08-20 09:50:01"}"\n    [":data_source_id"]: int(73)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(686): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(426): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(143): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 886\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:01\"}",":data_source_id":73},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(686): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(426): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(143): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 690\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:01\"}",":data_source_id":73}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 426\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:01\"}",":data_source_id":73}\n-->\n
[database_errors] [2025-08-20 09:50:06] [database.class.php:886] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":73,"created_at":"2025-08-20 09:50:06"}"\n    [":data_source_id"]: int(73)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(686): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(426): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(143): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 886\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:06\"}",":data_source_id":73},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(686): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(426): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(143): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 690\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:06\"}",":data_source_id":73}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 426\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:06\"}",":data_source_id":73}\n-->\n
[database_errors] [2025-08-20 09:50:10] [database.class.php:886] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":73,"created_at":"2025-08-20 09:50:10"}"\n    [":data_source_id"]: int(73)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(686): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(426): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(143): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 886\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:10\"}",":data_source_id":73},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(686): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(426): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(143): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 690\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:10\"}",":data_source_id":73}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 426\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":73,\"created_at\":\"2025-08-20 09:50:10\"}",":data_source_id":73}\n-->\n
[database_errors] [2025-08-20 10:01:18] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":74,"created_at":"2025-08-20 10:01:18"}"\n    [":data_source_id"]: int(74)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(450): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(143): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":74,\"created_at\":\"2025-08-20 10:01:18\"}",":data_source_id":74},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(450): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(143): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":74,\"created_at\":\"2025-08-20 10:01:18\"}",":data_source_id":74}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 450\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":74,\"created_at\":\"2025-08-20 10:01:18\"}",":data_source_id":74}\n-->\n
[database_errors] [2025-08-20 10:01:26] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":74,"created_at":"2025-08-20 10:01:26"}"\n    [":data_source_id"]: int(74)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(450): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(143): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":74,\"created_at\":\"2025-08-20 10:01:26\"}",":data_source_id":74},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(450): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(143): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":74,\"created_at\":\"2025-08-20 10:01:26\"}",":data_source_id":74}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 450\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":74,\"created_at\":\"2025-08-20 10:01:26\"}",":data_source_id":74}\n-->\n
[database_errors] [2025-08-20 10:03:37] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":75,"created_at":"2025-08-20 10:03:37"}"\n    [":data_source_id"]: int(75)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(450): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(143): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":75,\"created_at\":\"2025-08-20 10:03:37\"}",":data_source_id":75},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(450): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(143): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":75,\"created_at\":\"2025-08-20 10:03:37\"}",":data_source_id":75}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 450\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":75,\"created_at\":\"2025-08-20 10:03:37\"}",":data_source_id":75}\n-->\n
[database_errors] [2025-08-20 10:03:40] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "23000"\n  ["error_message"]: string(127) "SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["sql_state"]: string(5) "23000"\n  ["driver_error_code"]: int(1062)\n  ["driver_error_message"]: string(73) "Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"\n  ["query"]: string(165) "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n  ["parameters"]: array(4) {\n    [":table_name"]: string(30) "autobooks_import_sketchup_data"\n    [":user_id"]: int(2)\n    [":configuration"]: string(2596) "{"structure":[],"columns":[{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true},{"label":"","field":null,"filter":true}],"data_source_type":"data_source","data_source_id":75,"created_at":"2025-08-20 10:03:40"}"\n    [":data_source_id"]: int(75)\n  }\n  ["table"]: string(28) "autobooks_data_table_storage"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(41) "/baffletrain/autocadlt/autobooks/sketchup"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(1150) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(450): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/data_table_storage.class.php(143): system\database->insert()\n#3 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\data_table_storage::save_configuration()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#7 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#8 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"23000","error_message":"SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","sql_state":"23000","driver_error_code":1062,"driver_error_message":"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'","query":"INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)","parameters":{":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":75,\"created_at\":\"2025-08-20 10:03:40\"}",":data_source_id":75},"table":"autobooks_data_table_storage","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/sketchup","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(450): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/data_table_storage.class.php(143): system\\database->insert()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/sketchup_a2abe3be5ada827027549ee7842c5f37.comp.php(149): system\\data_table_storage::save_configuration()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#8 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["23000",1062,"Duplicate entry 'autobooks_import_sketchup_data' for key 'idx_table_name'"]}\n         1: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         2: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":75,\"created_at\":\"2025-08-20 10:03:40\"}",":data_source_id":75}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 450\n         <strong>Arguments:</strong> \n         0: "INSERT INTO autobooks_data_table_storage (`table_name`, `user_id`, `configuration`, `data_source_id`) VALUES (:table_name, :user_id, :configuration, :data_source_id)"\n         1: {":table_name":"autobooks_import_sketchup_data",":user_id":2,":configuration":"{\"structure\":[],\"columns\":[{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true},{\"label\":\"\",\"field\":null,\"filter\":true}],\"data_source_type\":\"data_source\",\"data_source_id\":75,\"created_at\":\"2025-08-20 10:03:40\"}",":data_source_id":75}\n-->\n
[database_errors] [2025-08-20 10:20:09] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(111) "SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1091)\n  ["driver_error_message"]: string(55) "Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["query"]: string(66) "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(39): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists","sql_state":"42000","driver_error_code":1091,"driver_error_message":"Can't DROP INDEX `idx_table_name`; check that it exists","query":"ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(39): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1091,"Can't DROP INDEX `idx_table_name`; check that it exists"]}\n         1: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:20:09] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(96) "SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1061)\n  ["driver_error_message"]: string(40) "Duplicate key name 'idx_table_name_user'"\n  ["query"]: string(103) "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(51): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'","sql_state":"42000","driver_error_code":1061,"driver_error_message":"Duplicate key name 'idx_table_name_user'","query":"ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(51): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1061,"Duplicate key name 'idx_table_name_user'"]}\n         1: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:22:41] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(111) "SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1091)\n  ["driver_error_message"]: string(55) "Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["query"]: string(66) "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(39): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists","sql_state":"42000","driver_error_code":1091,"driver_error_message":"Can't DROP INDEX `idx_table_name`; check that it exists","query":"ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(39): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1091,"Can't DROP INDEX `idx_table_name`; check that it exists"]}\n         1: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:22:41] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(96) "SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1061)\n  ["driver_error_message"]: string(40) "Duplicate key name 'idx_table_name_user'"\n  ["query"]: string(103) "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(51): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'","sql_state":"42000","driver_error_code":1061,"driver_error_message":"Duplicate key name 'idx_table_name_user'","query":"ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(51): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1061,"Duplicate key name 'idx_table_name_user'"]}\n         1: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:52:33] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(111) "SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1091)\n  ["driver_error_message"]: string(55) "Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["query"]: string(66) "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(39): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists","sql_state":"42000","driver_error_code":1091,"driver_error_message":"Can't DROP INDEX `idx_table_name`; check that it exists","query":"ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(39): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1091,"Can't DROP INDEX `idx_table_name`; check that it exists"]}\n         1: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:52:33] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(96) "SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1061)\n  ["driver_error_message"]: string(40) "Duplicate key name 'idx_table_name_user'"\n  ["query"]: string(103) "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(51): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'","sql_state":"42000","driver_error_code":1061,"driver_error_message":"Duplicate key name 'idx_table_name_user'","query":"ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(51): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1061,"Duplicate key name 'idx_table_name_user'"]}\n         1: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:52:48] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(111) "SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1091)\n  ["driver_error_message"]: string(55) "Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["query"]: string(66) "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(39): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists","sql_state":"42000","driver_error_code":1091,"driver_error_message":"Can't DROP INDEX `idx_table_name`; check that it exists","query":"ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(39): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1091,"Can't DROP INDEX `idx_table_name`; check that it exists"]}\n         1: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:52:48] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(96) "SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1061)\n  ["driver_error_message"]: string(40) "Duplicate key name 'idx_table_name_user'"\n  ["query"]: string(103) "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(51): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'","sql_state":"42000","driver_error_code":1061,"driver_error_message":"Duplicate key name 'idx_table_name_user'","query":"ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(51): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1061,"Duplicate key name 'idx_table_name_user'"]}\n         1: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:52:49] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(111) "SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1091)\n  ["driver_error_message"]: string(55) "Can't DROP INDEX `idx_table_name`; check that it exists"\n  ["query"]: string(66) "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(39): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP INDEX `idx_table_name`; check that it exists","sql_state":"42000","driver_error_code":1091,"driver_error_message":"Can't DROP INDEX `idx_table_name`; check that it exists","query":"ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(39): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1091,"Can't DROP INDEX `idx_table_name`; check that it exists"]}\n         1: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage DROP INDEX idx_table_name"\n         1: []\n-->\n
[database_errors] [2025-08-20 10:52:49] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42000"\n  ["error_message"]: string(96) "SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'"\n  ["sql_state"]: string(5) "42000"\n  ["driver_error_code"]: int(1061)\n  ["driver_error_message"]: string(40) "Duplicate key name 'idx_table_name_user'"\n  ["query"]: string(103) "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(7) "Unknown"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(60) "/baffletrain/autocadlt/autobooks/fix_database_constraint.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(440) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(728): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(999): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/fix_database_constraint.php(51): system\database::rawQuery()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42000","error_message":"SQLSTATE[42000]: Syntax error or access violation: 1061 Duplicate key name 'idx_table_name_user'","sql_state":"42000","driver_error_code":1061,"driver_error_message":"Duplicate key name 'idx_table_name_user'","query":"ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)","parameters":[],"table":"Unknown","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(728): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(999): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/fix_database_constraint.php(51): system\\database::rawQuery()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42000",1061,"Duplicate key name 'idx_table_name_user'"]}\n         1: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 999\n         <strong>Arguments:</strong> \n         0: "ALTER TABLE autobooks_data_table_storage ADD UNIQUE KEY `idx_table_name_user` (`table_name`, `user_id`)"\n         1: []\n-->\n
[database_errors] [2025-08-22 12:14:00] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
[database_errors] [2025-08-22 12:14:09] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
[database_errors] [2025-08-22 12:15:24] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
[database_errors] [2025-08-22 12:15:27] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
[database_errors] [2025-08-22 12:15:33] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
[database_errors] [2025-08-22 12:15:47] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
[database_errors] [2025-08-22 12:15:53] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
[database_errors] [2025-08-22 12:17:47] [database.class.php:928] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(130) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(78) "Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"\n  ["query"]: string(116) "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n  ["parameters"]: array(1) {\n    [":where_configuration_name_0"]: string(7) "default"\n  }\n  ["table"]: string(37) "autobooks_default_field_configuration"\n  ["user_id"]: int(2)\n  ["request_uri"]: string(67) "/baffletrain/autocadlt/autobooks/system/subscription_matching_rules"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko/20100101 Firefox/142.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(2134) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(727): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(284): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(315): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/default_field_configuration_manager.class.php(59): system\database->first()\n#4 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#6 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#7 /var/www/vhosts/cadservices.co.uk/temp/autobooks/edge/field-definitions-section.edge.php(43): edge\edge::render()\n#8 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#9 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(136): edge\edge::phprender()\n#10 /var/www/vhosts/cadservices.co.uk/temp/autobooks/views/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\edge::render()\n#11 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(143): include('...')\n#12 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/edge/edge.class.php(177): edge\edge::phprender()\n#13 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/router.class.php(202): edge\edge::renderView()\n#14 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(31): system\router::route()\n#15 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 928\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist","query":"SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1","parameters":{":where_configuration_name_0":"default"},"table":"autobooks_default_field_configuration","user_id":2,"request_uri":"\/baffletrain\/autocadlt\/autobooks\/system\/subscription_matching_rules","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:142.0) Gecko\/20100101 Firefox\/142.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(727): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(284): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(315): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/default_field_configuration_manager.class.php(59): system\\database->first()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/default-field-configuration-widget.edge.php(17): default_field_configuration_manager::get_default_configuration()\n#5 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#6 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#7 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/edge\/field-definitions-section.edge.php(43): edge\\edge::render()\n#8 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#9 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(136): edge\\edge::phprender()\n#10 \/var\/www\/vhosts\/cadservices.co.uk\/temp\/autobooks\/views\/subscription_matching_rules_0aeca2d7fa99154b33187a09ff09968d.comp.php(154): edge\\edge::render()\n#11 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(143): include('...')\n#12 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/edge\/edge.class.php(177): edge\\edge::phprender()\n#13 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/router.class.php(202): edge\\edge::renderView()\n#14 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(31): system\\router::route()\n#15 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 732\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_default_field_configuration' doesn't exist"]}\n         1: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         2: {":where_configuration_name_0":"default"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 284\n         <strong>Arguments:</strong> \n         0: "SELECT * FROM autobooks_default_field_configuration WHERE `configuration_name` = :where_configuration_name_0 LIMIT 1"\n         1: {":where_configuration_name_0":"default"}\n-->\n
