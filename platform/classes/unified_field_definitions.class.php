<?php

namespace system;

/**
 * Unified Field Definitions
 * 
 * Centralized field definition system that provides comprehensive metadata for all fields
 * used across subscription matching, data importing, and other systems.
 * 
 * This replaces the scattered field definitions in subscription_matching_rules and 
 * unified_field_mapper with a single source of truth.
 */
class unified_field_definitions {

    private static $log_target = 'unified_field_definitions';
    private static $table_name = 'autobooks_unified_field_definitions';
    
    /**
     * Master field definitions with comprehensive metadata
     * Each field includes:
     * - label: Human-readable field name
     * - type: Data type (string, email, date, number, etc.)
     * - patterns: Array of possible field name variations
     * - normalized_fields: Standard field names to map to
     * - validation: Validation rules
     * - matching: Matching configuration for subscription matching
     * - description: Field description for UI
     * - category: Field category for organization
     */
    private static $field_definitions = [
        
        // === CORE IDENTIFICATION FIELDS ===
        'subscription_reference' => [
            'label' => 'Subscription Reference',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Unique subscription identifier or reference number',
            'patterns' => [
                'subscription_reference', 'subscription_id', 'agreement_number', 'contract_number',
                'license_key', 'serial_number', 'reference_number', 'subscription_number',
                'order_number', 'ref', 'reference', 'subs_subscriptionReferenceNumber'
            ],
            'normalized_fields' => ['subscription_reference', 'subs_subscriptionReferenceNumber', 'subscription_id'],
            'validation' => [
                'required' => false,
                'min_length' => 3,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 0, // Highest priority
                'confidence_score' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true,
                'fuzzy_matching' => false
            ],
            'display' => [
                'show_by_default' => true, // Always show subscription reference by default
                'category_priority' => 1   // High priority for display
            ]
        ],
        
        'email' => [
            'label' => 'Email Address',
            'type' => 'email',
            'category' => 'contact',
            'description' => 'Primary contact email address',
            'patterns' => [
                'email', 'email_address', 'contact_email', 'admin_email', 'customer_email',
                'end_customer_contact_email', 'subscription_contact_email', 'endcust_primary_admin_email',
                'primary_email', 'business_email', 'work_email'
            ],
            'normalized_fields' => ['email_address', 'endcust_primary_admin_email', 'end_customer_contact_email', 'subscription_contact_email'],
            'validation' => [
                'required' => false,
                'format' => 'email'
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 1,
                'confidence_score' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true,
                'fuzzy_matching' => false
            ], 
            'display' => [
                'show_by_default' => true, // Always show company name by default
                'category_priority' => 1   // High priority for display
            ]
        ],
        
        'company_name' => [
            'label' => 'Company Name',
            'type' => 'string',
            'category' => 'organization',
            'description' => 'Organization or company name',
            'patterns' => [
                'company', 'company_name', 'customer', 'customer_name', 'client', 'client_name',
                'account_name', 'end_customer_name', 'endcust_name', 'organization', 'organization_name',
                'business_name', 'firm_name', 'name'
            ],
            'normalized_fields' => ['company_name', 'endcust_name', 'end_customer_name'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 2,
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'exact_match_only' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 70,
                'preprocessing' => [
                    'remove_common_words' => ['ltd', 'limited', 'inc', 'corp', 'corporation', 'llc', 'plc'],
                    'normalize_spaces' => true,
                    'remove_punctuation' => true
                ]
            ],
            'display' => [
                'show_by_default' => true, // Always show company name by default
                'category_priority' => 1   // High priority for display
            ]
        ],
        'vendor_id' => [
            'label' => 'Vendor Id',
            'type' => 'string',
            'category' => 'organization',
            'description' => 'Organization or company name',
            'patterns' => [
                'vendor_id'
            ],
            'normalized_fields' => ['vendor_id'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 2,
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'exact_match_only' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 70,
                'preprocessing' => [
                    'remove_common_words' => ['ltd', 'limited', 'inc', 'corp', 'corporation', 'llc', 'plc'],
                    'normalize_spaces' => true,
                    'remove_punctuation' => true
                ]
            ],
            'display' => [
                'show_by_default' => false, // Always show company name by default
                'category_priority' => 1   // High priority for display
            ]
        ],

        
        // === PRODUCT INFORMATION ===
        'product_name' => [
            'label' => 'Product Name',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Software or product name',
            'patterns' => [
                'product', 'product_name', 'software', 'license', 'offering', 'product_family',
                'agreement_program_name', 'service', 'application', 'tool', 'subs_offeringName'
            ],
            'normalized_fields' => ['product_name', 'subs_offeringName', 'agreement_program_name'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true, // Enable product_name matching
                'priority' => 1, // High priority for exact matches
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 80
            ],
            'display' => [
                'show_by_default' => true, // Always show product name by default
                'category_priority' => 1   // High priority for display
            ]
        ],
        
        'quantity' => [
            'label' => 'Quantity',
            'type' => 'number',
            'category' => 'product',
            'description' => 'Number of licenses or seats',
            'patterns' => [
                'quantity', 'qty', 'licenses', 'seats', 'subscription_quantity', 'users', 
                'count', 'license_count', 'seat_count', 'subs_quantity'
            ],
            'normalized_fields' => ['quantity', 'subs_quantity', 'subscription_quantity'],
            'validation' => [
                'required' => false,
                'min_value' => 0,
                'max_value' => 999999
            ],
            'matching' => [
                'enabled' => true, // Enable quantity matching
                'priority' => 6, // Medium priority
                'confidence_threshold' => 90,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false, // Hide quantity by default (technical field)
                'category_priority' => 3    // Lower priority for display
            ]
        ],
        
        // === DATE FIELDS ===
        'start_date' => [
            'label' => 'Start Date',
            'type' => 'date',
            'category' => 'dates',
            'description' => 'Subscription or agreement start date',
            'patterns' => [
                'start_date', 'date_start', 'purchase_date', 'agreement_start_date',
                'subscription_start_date', 'activation_date', 'begin_date', 'subs_startDate'
            ],
            'normalized_fields' => ['start_date', 'subs_startDate', 'agreement_start_date'],
            'validation' => [
                'required' => false,
                'format' => 'date'
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => true, // Always show start date by default
                'category_priority' => 1   // High priority for display
            ]
        ],

        'end_date' => [
            'label' => 'End Date',
            'type' => 'date',
            'category' => 'dates',
            'description' => 'Subscription or agreement end/expiry date',
            'patterns' => [
                'end_date', 'expiry_date', 'renewal_date', 'date_end', 'agreement_end_date',
                'subscription_end_date', 'expiration_date', 'due_date', 'subs_endDate'
            ],
            'normalized_fields' => ['end_date', 'subs_endDate', 'agreement_end_date'],
            'validation' => [
                'required' => false,
                'format' => 'date'
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => true, // Always show end date by default
                'category_priority' => 1   // High priority for display
            ]   
        ],

        // === CONTACT INFORMATION ===
        'contact_name' => [
            'label' => 'Contact Name',
            'type' => 'string',
            'category' => 'contact',
            'description' => 'Primary contact person name',
            'patterns' => [
                'contact', 'contact_name', 'name', 'customer_contact', 'end_customer_contact_name',
                'subscription_contact_name', 'admin_name', 'primary_contact', 'first_name', 'last_name'
            ],
            'normalized_fields' => ['contact_name', 'end_customer_contact_name', 'subscription_contact_name'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true, // Enable contact_name matching
                'priority' => 4, // Lower priority than company_name to avoid conflicts
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 80
            ],
            'display' => [
                'show_by_default' => true, // Always show contact name by default
                'category_priority' => 1   // High priority for display
            ]
        ],

        // === STATUS FIELDS ===
        'status' => [
            'label' => 'Status',
            'type' => 'string',
            'category' => 'status',
            'description' => 'Subscription or license status',
            'patterns' => [
                'status', 'subscription_status', 'agreement_status', 'active',
                'license_status', 'account_status', 'subscription_state', 'license_state', 'subs_status'
            ],
            'normalized_fields' => ['status', 'subs_status', 'subscription_status'],
            'validation' => [
                'required' => false,
                'allowed_values' => ['active', 'inactive', 'expired', 'suspended', 'cancelled', 'pending']
            ],
            'matching' => [
                'enabled' => true, // Enable status matching
                'priority' => 7, // Medium priority
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ],
            'display' => [
                'show_by_default' => true, // Always show status by default
                'category_priority' => 1   // High priority for display
            ]
        ],

        // === ADDRESS FIELDS ===
        'address' => [
            'label' => 'Address',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Street address or business address',
            'patterns' => [
                'address', 'address_1', 'street', 'end_customer_address_1', 'business_address',
                'shipping_address', 'billing_address', 'order_shipping_address'
            ],
            'normalized_fields' => ['address', 'end_customer_address_1'],
            'validation' => [
                'required' => false,
                'max_length' => 500
            ],
            'matching' => [
                'enabled' => true, // Enable address matching
                'priority' => 1, // High priority for address fields to prevent conflicts with company_name
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'fuzzy_matching' => true,
                'exact_match_only' => false
            ],
            'display' => [
                'show_by_default' => true, // Show this field by default when matched
                'category_priority' => 1   // Priority within category for display ordering
            ]
        ],

        'city' => [
            'label' => 'City',
            'type' => 'string',
            'category' => 'address',
            'description' => 'City or town name',
            'patterns' => [
                'city', 'end_customer_city', 'town', 'shipping_city', 'billing_city', 'order_shipping_city'
            ],
            'normalized_fields' => ['city', 'end_customer_city'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true, // Enable city matching
                'priority' => 4, // Medium priority for address components
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'state' => [
            'label' => 'State/Province',
            'type' => 'string',
            'category' => 'address',
            'description' => 'State, province, or region',
            'patterns' => [
                'state', 'province', 'region', 'end_customer_state', 'shipping_state',
                'billing_state', 'order_shipping_state_province', 'state_province'
            ],
            'normalized_fields' => ['state', 'end_customer_state'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true, // Enable state matching
                'priority' => 4, // Medium priority for address components
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => true, // Show this field by default when matched
                'category_priority' => 2   // Priority within category for display ordering
            ]
        ],

        'country' => [
            'label' => 'Country',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Country name',
            'patterns' => [
                'country', 'end_customer_country', 'nation', 'shipping_country',
                'billing_country', 'order_shipping_country'
            ],
            'normalized_fields' => ['country', 'end_customer_country'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true, // Enable country matching
                'priority' => 4, // Medium priority for address components
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => true, // Show this field by default when matched
                'category_priority' => 3   // Priority within category for display ordering
            ]
        ],

        'postal_code' => [
            'label' => 'Postal Code',
            'type' => 'string',
            'category' => 'address',
            'description' => 'ZIP code or postal code',
            'patterns' => [
                'zip', 'postal_code', 'zip_code', 'postcode', 'end_customer_zip_code',
                'shipping_zip', 'billing_zip'
            ],
            'normalized_fields' => ['postal_code', 'end_customer_zip_code'],
            'validation' => [
                'required' => false,
                'max_length' => 20
            ],
            'matching' => [
                'enabled' => true, // Enable postal code matching
                'priority' => 4, // Medium priority for address components
                'confidence_threshold' => 95,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => true, // Show this field by default when matched
                'category_priority' => 4   // Priority within category for display ordering
            ]
        ],

        // === PARTNER/RESELLER FIELDS ===
        'reseller_name' => [
            'label' => 'Reseller Name',
            'type' => 'string',
            'category' => 'partner',
            'description' => 'Reseller or partner company name',
            'patterns' => [
                'reseller', 'reseller_name', 'partner', 'partner_name',
                'account_primary_reseller_name', 'primary_reseller'
            ],
            'normalized_fields' => ['reseller_name', 'partner_name', 'account_primary_reseller_name'],
            'validation' => [
                'required' => false,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true, // Enable reseller_name matching
                'priority' => 3, // Medium priority
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => true, // Show this field by default when matched
                'category_priority' => 1   // Priority within category for display ordering
            ]
        ],

        // === SERIAL NUMBERS AND IDENTIFIERS ===
        'serial_number' => [
            'label' => 'Serial Number',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Product or license serial number',
            'patterns' => [
                'serial', 'serial_number', 'serial_no', 'product_serial', 'license_serial'
            ],
            'normalized_fields' => ['serial_number', 'product_serial', 'license_serial'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false, // Hide serial number by default
                'category_priority' => 5    // Lower priority for display
            ]
        ],

        // === FINANCIAL FIELDS ===
        'price' => [
            'label' => 'Price',
            'type' => 'currency',
            'category' => 'financial',
            'description' => 'Subscription or license price',
            'patterns' => [
                'price', 'cost', 'amount', 'subscription_price', 'license_cost',
                'total_price', 'unit_price', 'annual_price'
            ],
            'normalized_fields' => ['price', 'subscription_price', 'total_price'],
            'validation' => [
                'required' => false,
                'min_value' => 0
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false, // Hide price by default
                'category_priority' => 2    // Lower priority for display
            ]
        ],

        'currency' => [
            'label' => 'Currency',
            'type' => 'string',
            'category' => 'financial',
            'description' => 'Currency code (USD, EUR, etc.)',
            'patterns' => [
                'currency', 'currency_code', 'price_currency', 'product_list_price_currency'
            ],
            'normalized_fields' => ['currency', 'currency_code', 'product_list_price_currency'],
            'validation' => [
                'required' => false,
                'max_length' => 3,
                'allowed_values' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY']
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false
            ],
            'display' => [
                'show_by_default' => false, // Hide currency by default
                'category_priority' => 3    // Lower priority for display
            ]
        ],

        // === VENDOR AND RESELLER FIELDS ===
        'sold_to_name' => [
            'label' => 'Sold To Name',
            'type' => 'string',
            'category' => 'organization',
            'description' => 'Name of the organization the product was sold to',
            'patterns' => [
                'sold_to_name', 'sold_to', 'distributor_name', 'channel_partner_name',
                'sold_to_company', 'sold_to_company_name', 'sold_to_organization_name'
            ],
            'normalized_fields' => ['sold_to_name', 'sold_to'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 2,
                'confidence_threshold' => 85,
                'case_sensitive' => false,
                'exact_match_only' => false,
                'fuzzy_matching' => true,
                'similarity_threshold' => 70,
                'preprocessing' => [
                    'remove_common_words' => ['ltd', 'limited', 'inc', 'corp', 'corporation', 'llc', 'plc'],
                    'normalize_spaces' => true,
                    'remove_punctuation' => true
                ]
            ],
            'display' => [
                'show_by_default' => true,
                'category_priority' => 2
            ]
        ],

        'sold_to_number' => [
            'label' => 'Sold To Number',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Unique identifier for the sold-to organization',
            'patterns' => [
                'sold_to_number', 'sold_to_id', 'distributor_number', 'channel_partner_id'
            ],
            'normalized_fields' => ['sold_to_number', 'sold_to_id'],
            'validation' => [
                'required' => false,
                'min_length' => 2,
                'max_length' => 50
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 95,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 8
            ]
        ],

        'vendor_name' => [
            'label' => 'Vendor Name',
            'type' => 'string',
            'category' => 'organization',
            'description' => 'Name of the product vendor or manufacturer',
            'patterns' => [
                'vendor_name', 'vendor', 'manufacturer', 'publisher', 'supplier'
            ],
            'normalized_fields' => ['vendor_name'],
            'validation' => [
                'required' => false,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 6,
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => true,
                'category_priority' => 3
            ]
        ],

        'reseller_number' => [
            'label' => 'Reseller Number',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Unique identifier for the reseller',
            'patterns' => [
                'reseller_number', 'reseller_id', 'partner_number', 'channel_id'
            ],
            'normalized_fields' => ['reseller_number'],
            'validation' => [
                'required' => false,
                'max_length' => 50
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 9
            ]
        ],

        'reseller_vendor_id' => [
            'label' => 'Reseller Vendor ID',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Vendor-specific identifier for the reseller',
            'patterns' => [
                'reseller_vendor_id', 'partner_vendor_id', 'channel_vendor_id'
            ],
            'normalized_fields' => ['reseller_vendor_id'],
            'validation' => [
                'required' => false,
                'max_length' => 50
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 9
            ]
        ],

        'end_customer_vendor_id' => [
            'label' => 'End Customer Vendor ID',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Vendor-specific identifier for the end customer',
            'patterns' => [
                'end_customer_vendor_id', 'customer_vendor_id', 'endcust_vendor_id'
            ],
            'normalized_fields' => ['end_customer_vendor_id'],
            'validation' => [
                'required' => false,
                'max_length' => 50
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 9
            ]
        ],

        // === EXTENDED ADDRESS FIELDS ===
        'end_customer_address_2' => [
            'label' => 'Address Line 2',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Second line of customer address',
            'patterns' => [
                'end_customer_address_2', 'address_2', 'address_line_2', 'street_2'
            ],
            'normalized_fields' => ['end_customer_address_2', 'address_2'],
            'validation' => [
                'required' => false,
                'max_length' => 500
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 8
            ]
        ],

        'end_customer_address_3' => [
            'label' => 'Address Line 3',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Third line of customer address',
            'patterns' => [
                'end_customer_address_3', 'address_3', 'address_line_3', 'street_3'
            ],
            'normalized_fields' => ['end_customer_address_3', 'address_3'],
            'validation' => [
                'required' => false,
                'max_length' => 500
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 8
            ]
        ],

        'end_customer_city' => [
            'label' => 'City',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Customer city',
            'patterns' => [
                'end_customer_city', 'city', 'town', 'locality'
            ],
            'normalized_fields' => ['end_customer_city', 'city'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 85,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 7
            ]
        ],

        'end_customer_state' => [
            'label' => 'State/Province',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Customer state or province',
            'patterns' => [
                'end_customer_state', 'state', 'province', 'region'
            ],
            'normalized_fields' => ['end_customer_state', 'state'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 85,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 7
            ]
        ],

        'end_customer_zip_code' => [
            'label' => 'ZIP/Postal Code',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Customer ZIP or postal code',
            'patterns' => [
                'end_customer_zip_code', 'zip_code', 'postal_code', 'zip', 'postcode'
            ],
            'normalized_fields' => ['end_customer_zip_code', 'zip_code', 'postal_code'],
            'validation' => [
                'required' => false,
                'max_length' => 20
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 7
            ]
        ],

        'end_customer_country' => [
            'label' => 'Country',
            'type' => 'string',
            'category' => 'address',
            'description' => 'Customer country',
            'patterns' => [
                'end_customer_country', 'country', 'nation'
            ],
            'normalized_fields' => ['end_customer_country', 'country'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 6,
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 6
            ]
        ],

        // === CONTACT FIELDS ===
        'end_customer_account_type' => [
            'label' => 'Account Type',
            'type' => 'string',
            'category' => 'organization',
            'description' => 'Type of customer account (Commercial, Educational, etc.)',
            'patterns' => [
                'end_customer_account_type', 'account_type', 'customer_type', 'organization_type'
            ],
            'normalized_fields' => ['end_customer_account_type', 'account_type'],
            'validation' => [
                'required' => false,
                'max_length' => 50,
                'allowed_values' => ['Commercial', 'Educational', 'Government', 'Non-Profit', 'Personal']
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 7
            ]
        ],

        'end_customer_contact_name' => [
            'label' => 'Contact Name',
            'type' => 'string',
            'category' => 'contact',
            'description' => 'Primary contact person name',
            'patterns' => [
                'end_customer_contact_name', 'contact_name', 'primary_contact', 'admin_name',
                'subscription_contact_name', 'customer_contact'
            ],
            'normalized_fields' => ['end_customer_contact_name', 'contact_name', 'subscription_contact_name'],
            'validation' => [
                'required' => false,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 5,
                'confidence_threshold' => 85,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => true,
                'category_priority' => 1
            ]
        ],

        'end_customer_contact_phone' => [
            'label' => 'Contact Phone',
            'type' => 'string',
            'category' => 'contact',
            'description' => 'Primary contact phone number',
            'patterns' => [
                'end_customer_contact_phone', 'contact_phone', 'phone', 'telephone', 'phone_number'
            ],
            'normalized_fields' => ['end_customer_contact_phone', 'contact_phone', 'phone'],
            'validation' => [
                'required' => false,
                'max_length' => 50
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 6,
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 6
            ]
        ],

        'end_customer_industry_segment' => [
            'label' => 'Industry Segment',
            'type' => 'string',
            'category' => 'organization',
            'description' => 'Customer industry or business segment',
            'patterns' => [
                'end_customer_industry_segment', 'industry_segment', 'industry', 'business_segment', 'sector'
            ],
            'normalized_fields' => ['end_customer_industry_segment', 'industry'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 8
            ]
        ],

        // === AGREEMENT FIELDS ===
        'agreement_program_name' => [
            'label' => 'Agreement Program Name',
            'type' => 'string',
            'category' => 'agreement',
            'description' => 'Name of the agreement or program',
            'patterns' => [
                'agreement_program_name', 'program_name', 'agreement_name', 'contract_program'
            ],
            'normalized_fields' => ['agreement_program_name', 'program_name'],
            'validation' => [
                'required' => false,
                'max_length' => 255
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 85,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 7
            ]
        ],

        'agreement_terms' => [
            'label' => 'Agreement Terms',
            'type' => 'string',
            'category' => 'agreement',
            'description' => 'Terms of the agreement (Annual, Monthly, etc.)',
            'patterns' => [
                'agreement_terms', 'contract_terms', 'terms', 'billing_terms'
            ],
            'normalized_fields' => ['agreement_terms', 'terms'],
            'validation' => [
                'required' => false,
                'max_length' => 100,
                'allowed_values' => ['Annual', 'Monthly', 'Quarterly', 'Multi-Year']
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 7
            ]
        ],

        'agreement_type' => [
            'label' => 'Agreement Type',
            'type' => 'string',
            'category' => 'agreement',
            'description' => 'Type of agreement or contract',
            'patterns' => [
                'agreement_type', 'contract_type', 'license_type'
            ],
            'normalized_fields' => ['agreement_type', 'contract_type'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 7
            ]
        ],

        'agreement_status' => [
            'label' => 'Agreement Status',
            'type' => 'string',
            'category' => 'agreement',
            'description' => 'Current status of the agreement',
            'patterns' => [
                'agreement_status', 'contract_status', 'license_status'
            ],
            'normalized_fields' => ['agreement_status', 'contract_status'],
            'validation' => [
                'required' => false,
                'max_length' => 50,
                'allowed_values' => ['ACTIVE', 'EXPIRED', 'SUSPENDED', 'CANCELLED', 'PENDING']
            ],
            'matching' => [
                'enabled' => true,
                'priority' => 6,
                'confidence_threshold' => 90,
                'case_sensitive' => false
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 6
            ]
        ],

        'agreement_support_level' => [
            'label' => 'Agreement Support Level',
            'type' => 'string',
            'category' => 'agreement',
            'description' => 'Support level included in the agreement',
            'patterns' => [
                'agreement_support_level', 'support_level', 'service_level', 'support_tier'
            ],
            'normalized_fields' => ['agreement_support_level', 'support_level'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 85,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'agreement_days_due' => [
            'label' => 'Agreement Days Due',
            'type' => 'number',
            'category' => 'agreement',
            'description' => 'Number of days until agreement expires or renewal is due',
            'patterns' => [
                'agreement_days_due', 'days_due', 'days_to_expiry', 'renewal_days'
            ],
            'normalized_fields' => ['agreement_days_due', 'days_due'],
            'validation' => [
                'required' => false,
                'min_value' => -999,
                'max_value' => 9999
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
            ],
        ],

        'agreement_autorenew' => [
            'label' => 'Agreement Auto-Renew',
            'type' => 'string',
            'category' => 'agreement',
            'description' => 'Whether the agreement automatically renews',
            'patterns' => [
                'agreement_autorenew', 'auto_renew', 'autorenew', 'automatic_renewal'
            ],
            'normalized_fields' => ['agreement_autorenew', 'auto_renew'],
            'validation' => [
                'required' => false,
                'allowed_values' => ['0', '1', 'true', 'false', 'yes', 'no']
            ],
             'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        // === PRODUCT FIELDS ===
        'product_family' => [
            'label' => 'Product Family',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Product family or category',
            'patterns' => [
                'product_family', 'product_category', 'product_line', 'family'
            ],
            'normalized_fields' => ['product_family', 'product_category'],
            'validation' => [
                'required' => false,
                'max_length' => 255
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 6,
                'confidence_threshold' => 85,
                'case_sensitive' => false,
                'fuzzy_matching' => true
            
            ], 'display' => [
                'show_by_default' => false,
                'category_priority' => 1   // High priority for display
            ]
        ],

        'product_market_segment' => [
            'label' => 'Product Market Segment',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Target market segment for the product',
            'patterns' => [
                'product_market_segment', 'market_segment', 'target_market', 'segment'
            ],
            'normalized_fields' => ['product_market_segment', 'market_segment'],
            'validation' => [
                'required' => false,
                'max_length' => 255
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'product_release' => [
            'label' => 'Product Release',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Product version or release number',
            'patterns' => [
                'product_release', 'product_version', 'version', 'release'
            ],
            'normalized_fields' => ['product_release', 'product_version'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'product_type' => [
            'label' => 'Product Type',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Type of product (Subscription, License, etc.)',
            'patterns' => [
                'product_type', 'license_type', 'offering_type'
            ],
            'normalized_fields' => ['product_type', 'license_type'],
            'validation' => [
                'required' => false,
                'max_length' => 100,
                'allowed_values' => ['Subscription', 'License', 'Perpetual', 'Trial']
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 90,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'product_deployment' => [
            'label' => 'Product Deployment',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Deployment method or type for the product',
            'patterns' => [
                'product_deployment', 'deployment', 'deployment_type'
            ],
            'normalized_fields' => ['product_deployment', 'deployment'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 85,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'product_sku' => [
            'label' => 'Product SKU',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Stock Keeping Unit identifier for the product',
            'patterns' => [
                'product_sku', 'sku', 'part_number', 'item_number'
            ],
            'normalized_fields' => ['product_sku', 'sku'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 5,
                'confidence_threshold' => 95,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'product_sku_description' => [
            'label' => 'Product SKU Description',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Detailed description of the product SKU',
            'patterns' => [
                'product_sku_description', 'sku_description', 'product_description', 'item_description'
            ],
            'normalized_fields' => ['product_sku_description', 'product_description'],
            'validation' => [
                'required' => false,
                'max_length' => 1000
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 80,
                'case_sensitive' => false,
                'fuzzy_matching' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'product_part' => [
            'label' => 'Product Part',
            'type' => 'string',
            'category' => 'product',
            'description' => 'Product part number or identifier',
            'patterns' => [
                'product_part', 'part', 'part_number', 'component'
            ],
            'normalized_fields' => ['product_part', 'part_number'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'product_list_price' => [
            'label' => 'Product List Price',
            'type' => 'currency',
            'category' => 'financial',
            'description' => 'Official list price of the product',
            'patterns' => [
                'product_list_price', 'list_price', 'price', 'unit_price', 'msrp'
            ],
            'normalized_fields' => ['product_list_price', 'list_price', 'price'],
            'validation' => [
                'required' => false,
                'min_value' => 0,
                'max_value' => 999999.99
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 6,
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        // === SUBSCRIPTION FIELDS ===
        'subscription_serial_number' => [
            'label' => 'Subscription Serial Number',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Serial number associated with the subscription',
            'patterns' => [
                'subscription_serial_number', 'serial_number', 'license_serial', 'subscription_serial'
            ],
            'normalized_fields' => ['subscription_serial_number', 'serial_number'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 4,
                'confidence_threshold' => 95,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'subscription_quantity' => [
            'label' => 'Subscription Quantity',
            'type' => 'number',
            'category' => 'subscription',
            'description' => 'Number of licenses or seats in the subscription',
            'patterns' => [
                'subscription_quantity', 'quantity', 'seats', 'licenses', 'users'
            ],
            'normalized_fields' => ['subscription_quantity', 'quantity'],
            'validation' => [
                'required' => false,
                'min_value' => 1,
                'max_value' => 99999
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 6,
                'confidence_threshold' => 90,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'subscription_level' => [
            'label' => 'Subscription Level',
            'type' => 'string',
            'category' => 'subscription',
            'description' => 'Level or tier of the subscription',
            'patterns' => [
                'subscription_level', 'service_level', 'tier', 'level'
            ],
            'normalized_fields' => ['subscription_level', 'service_level'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 85,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'subscription_days_due' => [
            'label' => 'Subscription Days Due',
            'type' => 'number',
            'category' => 'subscription',
            'description' => 'Number of days until subscription expires or renewal is due',
            'patterns' => [
                'subscription_days_due', 'days_due', 'days_to_expiry', 'renewal_days'
            ],
            'normalized_fields' => ['subscription_days_due', 'days_due'],
            'validation' => [
                'required' => false,
                'min_value' => -999,
                'max_value' => 9999
            ],
             'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        // === QUOTATION FIELDS ===
        'quotation_id' => [
            'label' => 'Quotation ID',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Unique identifier for the quotation',
            'patterns' => [
                'quotation_id', 'quote_id', 'quote_number', 'proposal_id'
            ],
            'normalized_fields' => ['quotation_id', 'quote_id'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 5,
                'confidence_threshold' => 95,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'quotation_type' => [
            'label' => 'Quotation Type',
            'type' => 'string',
            'category' => 'quotation',
            'description' => 'Type of quotation or quote',
            'patterns' => [
                'quotation_type', 'quote_type', 'proposal_type'
            ],
            'normalized_fields' => ['quotation_type', 'quote_type'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 85,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'quotation_vendor_id' => [
            'label' => 'Quotation Vendor ID',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Vendor identifier associated with the quotation',
            'patterns' => [
                'quotation_vendor_id', 'quote_vendor_id', 'vendor_quote_id'
            ],
            'normalized_fields' => ['quotation_vendor_id', 'quote_vendor_id'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'quotation_deal_registration_number' => [
            'label' => 'Deal Registration Number',
            'type' => 'string',
            'category' => 'identification',
            'description' => 'Deal registration number for the quotation',
            'patterns' => [
                'quotation_deal_registration_number', 'deal_registration_number', 'deal_reg_number'
            ],
            'normalized_fields' => ['quotation_deal_registration_number', 'deal_registration_number'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'quotation_status' => [
            'label' => 'Quotation Status',
            'type' => 'string',
            'category' => 'quotation',
            'description' => 'Current status of the quotation',
            'patterns' => [
                'quotation_status', 'quote_status', 'proposal_status'
            ],
            'normalized_fields' => ['quotation_status', 'quote_status'],
            'validation' => [
                'required' => false,
                'max_length' => 100,
                'allowed_values' => ['Draft', 'Sent', 'Accepted', 'Rejected', 'Expired', 'Urgent Renewal']
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 7,
                'confidence_threshold' => 90,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'quotation_resellerpo_previous' => [
            'label' => 'Previous Reseller PO',
            'type' => 'string',
            'category' => 'quotation',
            'description' => 'Previous reseller purchase order number',
            'patterns' => [
                'quotation_resellerpo_previous', 'previous_po', 'reseller_po_previous'
            ],
            'normalized_fields' => ['quotation_resellerpo_previous', 'previous_po'],
            'validation' => [
                'required' => false,
                'max_length' => 100
            ],
             'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'quotation_due_date' => [
            'label' => 'Quotation Due Date',
            'type' => 'date',
            'category' => 'dates',
            'description' => 'Due date for the quotation response',
            'patterns' => [
                'quotation_due_date', 'quote_due_date', 'due_date', 'response_due_date'
            ],
            'normalized_fields' => ['quotation_due_date', 'due_date'],
            'validation' => [
                'required' => false,
                'format' => 'date'
            ],
             'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 95,
                'case_sensitive' => false,
                'exact_match_only' => true
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        // === MISCELLANEOUS FIELDS ===
        'flaer_phase' => [
            'label' => 'FLAER Phase',
            'type' => 'string',
            'category' => 'status',
            'description' => 'FLAER phase or stage indicator',
            'patterns' => [
                'flaer_phase', 'phase', 'stage', 'lifecycle_phase'
            ],
            'normalized_fields' => ['flaer_phase', 'phase'],
            'validation' => [
                'required' => false,
                'max_length' => 100,
                'allowed_values' => ['Renew', 'New', 'Upgrade', 'Downgrade']
            ],
             'matching' => [
                'enabled' => true,
                'priority' => 8,
                'confidence_threshold' => 85,
                'case_sensitive' => false
                        
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 1             
            ]
        ],

        'updated' => [
            'label' => 'Last Updated',
            'type' => 'date',
            'category' => 'system',
            'description' => 'Date and time when the record was last updated',
            'patterns' => [
                'updated', 'last_updated', 'modified', 'last_modified', 'timestamp'
            ],
            'normalized_fields' => ['updated', 'last_updated'],
            'validation' => [
                'required' => false,
                'format' => 'datetime'
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 100,
                'case_sensitive' => false,
                'exact_match_only' => true
            ]
        ]
    ];

    /**
     * Get all field definitions
     */
    public static function get_all_fields(): array {
        return self::get_all_fields_with_overrides();
    }

    /**
     * Force refresh field definitions and clear all caches
     */
    public static function force_refresh(): void {
        tcs_log("Force refreshing field definitions and clearing caches", self::$log_target);

        // Clear field mapper cache
        if (class_exists('system\unified_field_mapper')) {
            \system\unified_field_mapper::clear_cache();
        }

        // Force reload by calling get_all_fields_with_overrides
        $fields = self::get_all_fields_with_overrides();
        tcs_log("Force refresh complete, loaded " . count($fields) . " field definitions", self::$log_target);
    }

    /**
     * Get field definition by name
     */
    public static function get_field(string $field_name): ?array {
        $all_fields = self::get_all_fields_with_overrides();
        return $all_fields[$field_name] ?? null;
    }

    /**
     * Get fields by category
     */
    public static function get_fields_by_category(string $category): array {
        $fields = [];
        $all_fields = self::get_all_fields_with_overrides();
        foreach ($all_fields as $name => $definition) {
            if ($definition['category'] === $category) {
                $fields[$name] = $definition;
            }
        }
        return $fields;
    }

    /**
     * Get all available categories
     */
    public static function get_categories(): array {
        $categories = [];
        $all_fields = self::get_all_fields_with_overrides();
        foreach ($all_fields as $definition) {
            $categories[] = $definition['category'];
        }
        return array_unique($categories);
    }

    /**
     * Get fields enabled for matching
     */
    public static function get_matching_fields(): array {
        $fields = [];
        $all_fields = self::get_all_fields_with_overrides();
        foreach ($all_fields as $name => $definition) {
            if ($definition['matching']['enabled'] ?? false) {
                $fields[$name] = $definition;
            }
        }
        return $fields;
    }

    /**
     * Get fields that should be shown by default in data tables
     */
    public static function get_default_display_fields(): array {
        $fields = [];
        $all_fields = self::get_all_fields_with_overrides();
        foreach ($all_fields as $name => $definition) {
            if ($definition['display']['show_by_default'] ?? false) {
                $fields[$name] = $definition;
            }
        }
        return $fields;
    }

    /**
     * Check if a field should be shown by default
     */
    public static function should_show_by_default(string $field_name): bool {
        $field = self::get_field($field_name);
        if (!$field) {
            return true; // Show unknown fields by default
        }

        return $field['display']['show_by_default'] ?? true;
    }

    /**
     * Normalize field name for matching - creates variations with spaces and underscores
     */
    private static function normalize_field_name(string $field_name): array {
        $normalized = strtolower(trim($field_name));

        // Create variations: original, with spaces, with underscores
        $variations = [
            $normalized,
            str_replace('_', ' ', $normalized),  // underscores to spaces
            str_replace(' ', '_', $normalized)   // spaces to underscores
        ];

        // Remove duplicates and return
        return array_unique($variations);
    }

    /**
     * Find field by pattern match (including space/underscore variations)
     */
    public static function find_field_by_pattern(string $pattern): ?string {
        $pattern_variations = self::normalize_field_name($pattern);

        $all_fields = self::get_all_fields_with_overrides();
        foreach ($all_fields as $field_name => $definition) {
            $patterns_lower = array_map('strtolower', $definition['patterns']);

            // Check each pattern variation against each field pattern
            foreach ($pattern_variations as $pattern_var) {
                if (in_array($pattern_var, $patterns_lower)) {
                    return $field_name;
                }
            }

            // Also check if any field pattern variations match the input pattern
            foreach ($patterns_lower as $field_pattern) {
                $field_pattern_variations = self::normalize_field_name($field_pattern);
                if (array_intersect($pattern_variations, $field_pattern_variations)) {
                    return $field_name;
                }
            }
        }

        return null;
    }

    /**
     * Get normalized field names for a field
     */
    public static function get_normalized_fields(string $field_name): array {
        $definition = self::get_field($field_name);
        return $definition['normalized_fields'] ?? [];
    }

    /**
     * Get field patterns for a field
     */
    public static function get_field_patterns(string $field_name): array {
        $definition = self::get_field($field_name);
        return $definition['patterns'] ?? [];
    }

    /**
     * Get matching configuration for a field
     */
    public static function get_matching_config(string $field_name): array {
        $definition = self::get_field($field_name);
        return $definition['matching'] ?? [];
    }

    /**
     * Validate field value according to field definition
     */
    public static function validate_field_value(string $field_name, $value): array {
        $definition = self::get_field($field_name);
        if (!$definition) {
            return ['valid' => false, 'errors' => ['Field definition not found']];
        }

        $validation = $definition['validation'] ?? [];
        $errors = [];

        // Required check
        if (($validation['required'] ?? false) && empty($value)) {
            $errors[] = 'Field is required';
        }

        if (!empty($value)) {
            // Type-specific validation
            switch ($definition['type']) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = 'Invalid email format';
                    }
                    break;

                case 'number':
                    if (!is_numeric($value)) {
                        $errors[] = 'Must be a number';
                    } else {
                        $num_value = (float)$value;
                        if (isset($validation['min_value']) && $num_value < $validation['min_value']) {
                            $errors[] = "Must be at least {$validation['min_value']}";
                        }
                        if (isset($validation['max_value']) && $num_value > $validation['max_value']) {
                            $errors[] = "Must be at most {$validation['max_value']}";
                        }
                    }
                    break;

                case 'date':
                    if (!strtotime($value)) {
                        $errors[] = 'Invalid date format';
                    }
                    break;

                case 'string':
                    $length = strlen($value);
                    if (isset($validation['min_length']) && $length < $validation['min_length']) {
                        $errors[] = "Must be at least {$validation['min_length']} characters";
                    }
                    if (isset($validation['max_length']) && $length > $validation['max_length']) {
                        $errors[] = "Must be at most {$validation['max_length']} characters";
                    }
                    break;
            }

            // Allowed values check
            if (isset($validation['allowed_values']) && !in_array(strtolower($value), array_map('strtolower', $validation['allowed_values']))) {
                $errors[] = 'Invalid value. Allowed: ' . implode(', ', $validation['allowed_values']);
            }
        }

        return ['valid' => empty($errors), 'errors' => $errors];
    }

    /**
     * Validate a complete field definition structure
     */
    public static function validate_field_definition(array $definition): array {
        $errors = [];

        // Required fields
        $required_fields = ['label', 'type', 'category', 'description', 'patterns', 'normalized_fields'];
        foreach ($required_fields as $field) {
            if (!isset($definition[$field]) || empty($definition[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }

        // Validate type
        $valid_types = ['string', 'email', 'date', 'number', 'currency'];
        if (isset($definition['type']) && !in_array($definition['type'], $valid_types)) {
            $errors[] = "Invalid type. Must be one of: " . implode(', ', $valid_types);
        }

        // Validate patterns array
        if (isset($definition['patterns']) && !is_array($definition['patterns'])) {
            $errors[] = "Patterns must be an array";
        }

        // Validate normalized_fields array
        if (isset($definition['normalized_fields']) && !is_array($definition['normalized_fields'])) {
            $errors[] = "Normalized fields must be an array";
        }

        // Validate matching configuration if present
        if (isset($definition['matching'])) {
            $matching = $definition['matching'];
            if (isset($matching['priority']) && (!is_numeric($matching['priority']) || $matching['priority'] < 0)) {
                $errors[] = "Matching priority must be a non-negative number";
            }
            if (isset($matching['similarity_threshold']) && (!is_numeric($matching['similarity_threshold']) || $matching['similarity_threshold'] < 0 || $matching['similarity_threshold'] > 100)) {
                $errors[] = "Similarity threshold must be between 0 and 100";
            }
        }

        return ['valid' => empty($errors), 'errors' => $errors];
    }

    /**
     * Create a new field definition with default structure
     */
    public static function create_field_template(string $field_name): array {
        return [
            'label' => ucwords(str_replace('_', ' ', $field_name)),
            'type' => 'string',
            'category' => 'custom',
            'description' => 'Custom field definition',
            'patterns' => [$field_name],
            'normalized_fields' => [$field_name],
            'validation' => [
                'required' => false
            ],
            'matching' => [
                'enabled' => false,
                'priority' => 10,
                'confidence_threshold' => 70,
                'case_sensitive' => false,
                'exact_match_only' => false,
                'fuzzy_matching' => false,
                'similarity_threshold' => 70
            ],
            'display' => [
                'show_by_default' => false,
                'category_priority' => 10
            ]
        ];
    }

    /**
     * Check if a field definition is custom (not in defaults)
     */
    public static function is_custom_field(string $field_name): bool {
        return !isset(self::$field_definitions[$field_name]);
    }

    /**
     * Check if a field definition has been modified from default
     */
    public static function is_modified_field(string $field_name): bool {
        $current = self::get_field($field_name);
        $default = self::$field_definitions[$field_name] ?? null;

        if (!$default) {
            return true; // Custom field
        }

        return json_encode($current) !== json_encode($default);
    }

    /**
     * Get all field definitions (from database if available, otherwise defaults)
     */
    public static function get_all_fields_with_overrides(): array {
        try {
            if (!self::ensure_table_exists()) {
                tcs_log("Field definitions table does not exist, using defaults", self::$log_target);
                return self::$field_definitions;
            }

            // Get custom definitions from database
            $custom_fields = database::table(self::$table_name)
                ->where('is_active', 1)
                ->get();

            tcs_log("Loaded " . count($custom_fields) . " custom field definitions from database", self::$log_target);

            $fields = self::$field_definitions; // Start with defaults

            // Override with custom definitions
            foreach ($custom_fields as $custom_field) {
                $field_data = json_decode($custom_field['field_definition'], true);
                if ($field_data) {
                    $fields[$custom_field['field_name']] = $field_data;
                    tcs_log("Applied custom definition for field: {$custom_field['field_name']}", self::$log_target);
                }
            }

            // Log enabled fields for debugging
            $enabled_fields = [];
            foreach ($fields as $field_name => $definition) {
                if ($definition['matching']['enabled'] ?? false) {
                    $enabled_fields[] = $field_name;
                }
            }
            tcs_log("Total enabled fields: " . count($enabled_fields) . " (" . implode(', ', $enabled_fields) . ")", self::$log_target);

            return $fields;

        } catch (\Exception $e) {
            tcs_log("Error loading field definitions: " . $e->getMessage(), self::$log_target);
            return self::$field_definitions;
        }
    }

    /**
     * Save a field definition to database
     */
    public static function save_field_definition(string $field_name, array $definition): bool {
        try {
            if (!self::ensure_table_exists()) {
                return false;
            }

            $field_data = [
                'field_name' => $field_name,
                'field_definition' => json_encode($definition),
                'is_active' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Check if field exists
            $existing = database::table(self::$table_name)
                ->where('field_name', $field_name)
                ->first();

            if ($existing) {
                database::table(self::$table_name)
                    ->where('field_name', $field_name)
                    ->update($field_data);
            } else {
                $field_data['created_at'] = date('Y-m-d H:i:s');
                database::table(self::$table_name)->insert($field_data);
            }

            tcs_log("Saved field definition: {$field_name}", self::$log_target);

            // Clear field mapper cache to ensure changes are reflected
            if (class_exists('system\unified_field_mapper')) {
                unified_field_mapper::clear_cache();
            }

            return true;

        } catch (\Exception $e) {
            tcs_log("Error saving field definition {$field_name}: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Delete a field definition from database
     */
    public static function delete_field_definition(string $field_name): bool {
        try {
            if (!self::ensure_table_exists()) {
                return false;
            }

            database::table(self::$table_name)
                ->where('field_name', $field_name)
                ->update(['is_active' => 0, 'updated_at' => date('Y-m-d H:i:s')]);

            tcs_log("Deleted field definition: {$field_name}", self::$log_target);

            // Clear field mapper cache to ensure changes are reflected
            if (class_exists('system\unified_field_mapper')) {
                unified_field_mapper::clear_cache();
            }

            return true;

        } catch (\Exception $e) {
            tcs_log("Error deleting field definition {$field_name}: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Reset field definition to default
     */
    public static function reset_field_to_default(string $field_name): bool {
        try {
            if (!self::ensure_table_exists()) {
                return false;
            }

            database::table(self::$table_name)
                ->where('field_name', $field_name)
                ->update(['is_active' => 0, 'updated_at' => date('Y-m-d H:i:s')]);

            tcs_log("Reset field definition to default: {$field_name}", self::$log_target);

            // Clear field mapper cache to ensure changes are reflected
            if (class_exists('system\unified_field_mapper')) {
                unified_field_mapper::clear_cache();
            }

            return true;

        } catch (\Exception $e) {
            tcs_log("Error resetting field definition {$field_name}: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Ensure the field definitions table exists
     */
    private static function ensure_table_exists(): bool {
        try {
            if (database::tableExists(self::$table_name)) {
                return true;
            }

            $sql = "CREATE TABLE `" . self::$table_name . "` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `field_name` varchar(100) NOT NULL,
                `field_definition` longtext NOT NULL,
                `is_active` tinyint(1) NOT NULL DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `field_name` (`field_name`),
                KEY `is_active` (`is_active`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            database::rawQuery($sql);
            tcs_log("Created field definitions table: " . self::$table_name, self::$log_target);
            return true;

        } catch (\Exception $e) {
            tcs_log("Error creating field definitions table: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Get field definition statistics
     */
    public static function get_field_statistics(): array {
        $all_fields = self::get_all_fields_with_overrides();
        $default_fields = self::$field_definitions;

        $stats = [
            'total_fields' => count($all_fields),
            'default_fields' => count($default_fields),
            'custom_fields' => 0,
            'matching_enabled' => 0,
            'categories' => []
        ];

        foreach ($all_fields as $field_name => $definition) {
            // Count custom fields (not in defaults or modified)
            if (!isset($default_fields[$field_name]) ||
                json_encode($definition) !== json_encode($default_fields[$field_name])) {
                $stats['custom_fields']++;
            }

            // Count matching enabled fields
            if ($definition['matching']['enabled'] ?? false) {
                $stats['matching_enabled']++;
            }

            // Count by category
            $category = $definition['category'] ?? 'other';
            $stats['categories'][$category] = ($stats['categories'][$category] ?? 0) + 1;
        }

        return $stats;
    }
}
