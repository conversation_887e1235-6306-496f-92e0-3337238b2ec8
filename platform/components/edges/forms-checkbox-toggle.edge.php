@props([
    'title' => 'form checkbox toggle control',
    'description' => 'Basic form select',
    'value', // An array of items.
    'name' => '',
    'checked' => false,
    'label' => '',
    'extra_attributes' => '',
])
<div class="flex items-center">
    <label class="relative inline-flex items-center cursor-pointer">
        <input type="checkbox"
               name="{{ $name }}"
               class="sr-only peer"
               x-init="$el.checked='{{ $checked }}'"
               {{ $extra_attributes }}
        >
        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
        <span class="ms-3 text-sm font-medium text-gray-900">{{ $label }}</span>
        <!-- Small loading indicator -->
        <span id="debug-mode-indicator" class="htmx-indicator ml-2">
                        <svg class="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                             fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
    </label>
</div>

